export const DEFAULT_AUSTIN_COORDINATES = [30.2672, -97.7431];
export const DEFAULT_REPORTS_API_PAYLOAD = {
  year: '2025',
  month: '1',
  department: null,
  businessType: null,
  marketleader: null,
  adminBu: null,
};
export const StoreMemberTableName: Record<string, string> = {
  Store: 'str_store',
  Site: 'str_site',
  'Store Concept': 'str_storeConcept',
  District: 'str_district',
  Market: 'str_market',
  'Region District': 'str_regionDistrict',
  Region: 'str_region',
  Employee: 'emp_employee',
  Geography: 'str_geography',
  Department: 'emp_department',
  Job: 'emp_job',
  'Employee History': 'hst_employeeHistory',
};

export enum ROLES {
  ADMIN = 'Admin',
  DEVELOPER = 'Developer',
  READER = 'Reader',
}

export enum ReportDataType {
  TransactionWeekVsLastYear,
  TransactionFiscalWeek,
  SaleWeekVsLastYear,
  SalesFiscalWeek,
}

export const isAdmin = (userRoles: string[] | null): boolean => {
  if (!userRoles) return false;

  const adminRoles = [ROLES.ADMIN];

  return userRoles.some((role) => adminRoles.includes(role as ROLES));
};

export const isDeveloper = (userRoles: string[] | null): boolean => {
  if (!userRoles) return false;

  const devRoles = [ROLES.DEVELOPER];

  return userRoles.some((role) => devRoles.includes(role as ROLES));
};
