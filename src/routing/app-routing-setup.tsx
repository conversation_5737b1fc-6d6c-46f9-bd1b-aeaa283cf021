import { lazy, Suspense } from 'react';
import { RequireAuth } from '@/auth/require-auth';
import { Navigate, Route, Routes } from 'react-router';

// Lazy load all major components
const DashboardWrapper = lazy(
  () => import('@/pages/dashboards/Dashboard/DashboardWrapper'),
);
const PipelineDashboardWrapper = lazy(
  () => import('@/pages/dashboards/PipelineDashboard/PipelineDashboardWrapper'),
);
const AuthRouting = lazy(() =>
  import('@/auth/auth-routing').then((m) => ({ default: m.AuthRouting })),
);

const ErrorRouting = lazy(() =>
  import('@/errors/error-routing').then((m) => ({ default: m.ErrorRouting })),
);
const Demo1Layout = lazy(() =>
  import('@/layouts/demo1/layout').then((m) => ({ default: m.Demo1Layout })),
);
const StatementOfOperationsYTDWrapper = lazy(
  () =>
    import(
      '@/pages/report/IncomeReport/StatementOfOperations/StatementOfOperationsYTDWrapper'
    ),
);
const ManagedUnitsWaUnitsReportWrapper = lazy(
  () =>
    import(
      '@/pages/report/IncomeReport/managed-units-wa-units-report/ManagedUnitsWaUnitsReportWrapper'
    ),
);

const StatementOfOperationsByThousandWrapper = lazy(
  () =>
    import(
      '@/pages/report/IncomeReport/StatementOfOperationsByThousand/StatementOfOperationsByThousandWrapper'
    ),
);

const PropertyPerformanceScorecardWrapper = lazy(
  () =>
    import(
      '@/pages/report/PropertyPerformanceScorecard/PropertyPerformanceScorecardWrapper'
    ),
);

const ScorecardWrapper = lazy(
  () => import('@/pages/report/Scorecard/ScorecardWrapper'),
);

const StatementOfOperationsPTDWrapper = lazy(
  () =>
    import(
      '@/pages/report/IncomeReport/StatementOfOperationsPTD/StatementOfOperationsPTDWrapper'
    ),
);

const YTDDrillThroughWrapper = lazy(
  () =>
    import(
      '@/pages/report/IncomeReport/YTDDrillThrough/YTDDrillThroughWrapper'
    ),
);

const PTDDrillThroughWrapper = lazy(
  () =>
    import(
      '@/pages/report/IncomeReport/PTDDrillThrough/PTDDrillThroughWrapper'
    ),
);
const FinancialKPIWrapper = lazy(
  () => import('@/pages/report/FinancialKPI/FinancialKPIWrapper'),
);

const UnitWalkWrapper = lazy(
  () => import('@/pages/report/UnitWalk/UnitWalkWrapper'),
);

const PropertyManagementKPIsWrapper = lazy(
  () =>
    import(
      '@/pages/report/PropertyManagementKPIs/PropertyManagementKPIsWrapper'
    ),
);

const BusinessDevelopmentKPIsWrapper = lazy(
  () =>
    import(
      '@/pages/report/BusinessDevelopmentKPIs/BusinessDevelopmentKPIsWrapper'
    ),
);

const StaffingReportWrapper = lazy(
  () => import('@/pages/report/StaffingKPIs/StaffingReportWrapper'),
);

export function AppRoutingSetup() {
  return (
    <Suspense fallback={null}>
      <Routes>
        <Route element={<RequireAuth />}>
          <Route element={<Demo1Layout />}>
            <Route path="/" element={<DashboardWrapper />} />
            <Route path="/pipeline" element={<PipelineDashboardWrapper />} />
            <Route
              path="/statement-of-operations-ytd"
              element={<StatementOfOperationsYTDWrapper />}
            />
            <Route
              path="/statement-of-operations-by-thousand"
              element={<StatementOfOperationsByThousandWrapper />}
            />
            <Route
              path="/managed-units-wa-units-report"
              element={<ManagedUnitsWaUnitsReportWrapper />}
            />
            <Route
              path="/property-performance-scorecard"
              element={<PropertyPerformanceScorecardWrapper />}
            />
            <Route path="/financial-kpi" element={<FinancialKPIWrapper />} />
            <Route path="/scorecard" element={<ScorecardWrapper />} />
            <Route
              path="/statement-of-operations-ptd"
              element={<StatementOfOperationsPTDWrapper />}
            />
            <Route
              path="/ytd-drill-through"
              element={<YTDDrillThroughWrapper />}
            />
            <Route
              path="/ptd-drill-through"
              element={<PTDDrillThroughWrapper />}
            />

            <Route path="/unit-walk" element={<UnitWalkWrapper />} />
            <Route
              path="/property-management-kpis"
              element={<PropertyManagementKPIsWrapper />}
            />
            <Route
              path="/business-development-kpis"
              element={<BusinessDevelopmentKPIsWrapper />}
            />
            <Route path="/staffing-kpis" element={<StaffingReportWrapper />} />
          </Route>
        </Route>
        <Route path="error/*" element={<ErrorRouting />} />
        <Route path="auth/*" element={<AuthRouting />} />
        <Route path="*" element={<Navigate to="/error/404" />} />
      </Routes>
    </Suspense>
  );
}
