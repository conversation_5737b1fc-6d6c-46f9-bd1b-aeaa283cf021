// src/app/store.ts
import { configureStore } from '@reduxjs/toolkit';
import { combineReducers } from 'redux';
import { persistReducer, persistStore } from 'redux-persist';
import storage from 'redux-persist/lib/storage'; // defaults to localStorage for web

import authSlice from './slice/authSlice';
import businessDevelopmentSlice from './slice/businessDevelopmentSlice';
import { counterSlice } from './slice/counter';
import incomeReportSlice from './slice/incomeReportSlice';
import pipelineDashSlice from './slice/pipelineDashSlice';
import propertyManagementKpiSlice from './slice/propertyManagementKpiSlice';
import scorecardSlice from './slice/scoreCardSlice';
import unitWalkSlice from './slice/unitWalkSlice';
import staffingKpiSlice from './slice/staffingKpiSlice';

// import authSlice from "../__redux/authSlice";
// import generalSlice from "../__redux/generalSlice";
// import pipelineDashSlice from "../__redux/pipelineDashSlice";

const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['auth'],
};

const rootReducer = combineReducers({
  counter: counterSlice.reducer,
  auth: authSlice.reducer,
  pipelineDash: pipelineDashSlice.reducer,
  incomeReport: incomeReportSlice.reducer,
  scorecard: scorecardSlice,
  unitWalk: unitWalkSlice,
  propertyManagementKpi: propertyManagementKpiSlice,
  staffingKpi: staffingKpiSlice,
  businessDevelopment: businessDevelopmentSlice.reducer,
});

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

export const persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
