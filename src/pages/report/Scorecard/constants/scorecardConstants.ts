import { getFirstWeekOfPreviousMonth } from '../utils/dateUtils';

export const PAGE_BG = '#F4F4FF';
export const HEADER_COLOR = '#432890';
export const BUTTON_COLOR = '#43298F';

const { startDate: firstWeekStart, endDate: firstWeekEnd } =
  getFirstWeekOfPreviousMonth();
const defaultStartDate = firstWeekStart.format('YYYY-MM-DD');
const defaultEndDate = firstWeekEnd.format('YYYY-MM-DD');

export const DEFAULT_FILTERS = {
  system: 'N/A',
  property: '22514 - The Katy',
  startDate: defaultStartDate,
  endDate: defaultEndDate,
  reportingPeriod: `${firstWeekStart.format('M/D/YYYY')} - ${firstWeekEnd.format('M/D/YYYY')}`,
};

export const DEFAULT_API_PARAMS = {
  startDate: defaultStartDate,
  endDate: defaultEndDate,
  propertyCode: '22514',
};

export const SECTION_HEIGHTS = {
  metrics: '55%',
  performance: '45%',
};

export const PERFORMANCE_TABLE_FLEX = 4;
export const SUMMARY_SECTION_FLEX = 1;
