import React, { useEffect, useRef } from 'react';
import { RootState } from '@/store';
import { useSelector } from 'react-redux';
import { FilterValues, ScorecardFilters } from './components/filters';
import FinancialMetrics from './components/FinancialMetrics';
import OccupancyMetrics from './components/OccupancyMetrics';
import PerformanceTable from './components/PerformanceTable';
import PropertyInfo from './components/PropertyInfo';
import RentalMetrics from './components/RentalMetrics';
import ReputationSummary from './components/ReputationSummary';
import SummarySection from './components/SummarySection';
import YTDTurnCost from './components/YTDTurnCost';
import {
  HEADER_COLOR,
  PAGE_BG,
  SECTION_HEIGHTS,
} from './constants/scorecardConstants';
import {
  useScorecardData,
  useScorecardFilters,
  useScorecardPDFExport,
} from './hooks';
import { FilterOptionsResponse } from './types/scorecardTypes';

const ScorecardPage: React.FC = () => {
  const { title } = useSelector((state: RootState) => state.scorecard);
  const { fetchScorecardDataParallel, loadInitialDataParallel } =
    useScorecardData();
  const {
    filters,
    availableProperties,
    availableSystems,
    availableDates,
    rawSystemsData,
    minDate,
    maxDate,
    updateFilters,
    processFilterOptions,
  } = useScorecardFilters();
  const { exportToPDF, canExport, getExportStatus, isCapturing, isGenerating } =
    useScorecardPDFExport();

  const filterOptionsRef = useRef<FilterOptionsResponse | null>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const initializeData = async () => {
      await loadInitialDataParallel((filterOptionsResponse) => {
        filterOptionsRef.current = filterOptionsResponse;
        processFilterOptions(filterOptionsResponse);
      });
    };

    initializeData();
  }, [loadInitialDataParallel, processFilterOptions]);

  const handleApplyFilters = async (newFilters: FilterValues) => {
    const { propertyCode, startDate, endDate } = updateFilters(newFilters);

    if (propertyCode && startDate && endDate) {
      await fetchScorecardDataParallel(
        { propertyCode, startDate, endDate },
        (filterOptionsResponse) => {
          filterOptionsRef.current = filterOptionsResponse;
        },
      );
    }
  };

  const handleExportPDF = async () => {
    await exportToPDF(filters, contentRef);
  };

  return (
    <>
      <style>{`
        .scorecard-sidebar {
          width: clamp(240px, 15vw, 280px);
          flex-shrink: 0;
        }
      `}</style>
      <div className={`bg-[${PAGE_BG}] h-[100%]`}>
        <div
          ref={contentRef}
          data-screenshot-target="scorecard-content"
          className="flex h-full"
        >
          {/* Left Sidebar */}
          <div className="scorecard-sidebar">
            <PropertyInfo />
          </div>

          {/* Main Content */}
          <div className="flex-1 flex flex-col min-w-0">
            {/* Header */}
            <div className="bg-gray-50 px-3 sm:px-6 py-2 sm:py-3 border-b border-gray-200 flex-shrink-0">
              <div className="flex items-center justify-between">
                <h1
                  className="text-lg sm:text-xl font-semibold truncate"
                  style={{ color: HEADER_COLOR }}
                >
                  {title}
                </h1>
                <ScorecardFilters
                  filters={filters}
                  availableSystems={availableSystems}
                  availableProperties={availableProperties}
                  availableDates={availableDates}
                  rawSystemsData={rawSystemsData}
                  minDate={minDate}
                  maxDate={maxDate}
                  onApplyFilters={handleApplyFilters}
                  onExportPDF={handleExportPDF}
                  canExportPDF={canExport()}
                  exportStatus={getExportStatus()}
                  isExporting={isCapturing || isGenerating}
                />
              </div>
            </div>

            {/* Content Area */}
            <div className="flex-1 p-2 sm:p-3 flex flex-col min-h-0">
              <div
                className="flex flex-col gap-1 sm:gap-2"
                style={{ height: SECTION_HEIGHTS.metrics }}
              >
                <div className="flex-1 min-h-0">
                  <OccupancyMetrics />
                </div>
                <div className="flex-1 min-h-0">
                  <RentalMetrics />
                </div>
                <div className="flex-1 min-h-0">
                  <div className="grid grid-cols-[65fr_15fr_20fr] gap-2 sm:gap-3 h-full">
                    <div>
                      <FinancialMetrics />
                    </div>
                    <div>
                      <YTDTurnCost />
                    </div>
                    <div>
                      <ReputationSummary />
                    </div>
                  </div>
                </div>
              </div>

              {/* Performance Table and Summary */}
              <div
                className="grid grid-cols-[65fr_15fr_20fr] gap-2 sm:gap-3 mt-2"
                style={{ height: SECTION_HEIGHTS.performance }}
              >
                <div className="col-span-2">
                  <PerformanceTable />
                </div>
                <div>
                  <SummarySection />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ScorecardPage;
