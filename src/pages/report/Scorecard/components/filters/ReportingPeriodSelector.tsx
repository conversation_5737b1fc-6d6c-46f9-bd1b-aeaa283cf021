import React, { useCallback } from 'react';
import { DatePicker } from 'antd';
import dayjs, { Dayjs } from 'dayjs';

interface ReportingPeriodSelectorProps {
  startDate: string;
  endDate: string;
  onStartDateChange: (date: Dayjs | null) => void;
  onEndDateChange: (date: Dayjs | null) => void;
  minDate: Dayjs | null;
  maxDate: Dayjs | null;
  availableDates: Array<{
    label: string;
    value: string;
    dayjsValue?: Dayjs;
    monthKey?: string;
  }>;
}

export const ReportingPeriodSelector: React.FC<
  ReportingPeriodSelectorProps
> = ({
  startDate,
  endDate,
  onStartDateChange,
  onEndDateChange,
  minDate,
  maxDate,
  availableDates,
}) => {
  const convertISOToDayjs = (isoString: string): Dayjs | null => {
    if (!isoString) return null;
    return dayjs(isoString);
  };

  const getAvailableMonthKeys = useCallback(() => {
    return availableDates.map((d) => d.monthKey).filter(Boolean);
  }, [availableDates]);

  return (
    <div className="flex items-center space-x-1 sm:space-x-2">
      <span className="text-gray-600 hidden lg:inline">Reporting Period:</span>
      <div className="flex items-center space-x-1 sm:space-x-2">
        <DatePicker
          value={startDate ? convertISOToDayjs(startDate) : null}
          onChange={onStartDateChange}
          minDate={minDate || dayjs('2020-01-01')}
          maxDate={maxDate || dayjs('2030-12-31')}
          disabledDate={(current) => {
            if (!current || availableDates.length === 0) {
              return false;
            }
            const currentMonthKey = current.format('YYYY-MM');
            const availableMonthKeys = getAvailableMonthKeys();
            return !availableMonthKeys.includes(currentMonthKey);
          }}
          format="MM/DD/YYYY"
          className="max-w-42 sm:max-w-none"
          style={{
            height: '32px',
            fontSize: '12px',
          }}
          placeholder="Start Date"
        />
        <span className="text-gray-400 text-xs ml-2">to</span>
        <DatePicker
          value={endDate ? convertISOToDayjs(endDate) : null}
          onChange={onEndDateChange}
          minDate={minDate || dayjs('2020-01-01')}
          maxDate={maxDate || dayjs('2030-12-31')}
          disabledDate={(current) => {
            if (!current || availableDates.length === 0) return false;

            const currentMonthKey = current.format('YYYY-MM');
            const availableMonthKeys = getAvailableMonthKeys();

            const isAvailable = availableMonthKeys.includes(currentMonthKey);

            const startDateValue = startDate ? dayjs(startDate) : null;
            const isAfterStart =
              !startDateValue ||
              current.isAfter(startDateValue) ||
              current.isSame(startDateValue, 'day');

            return !isAvailable || !isAfterStart;
          }}
          format="MM/DD/YYYY"
          className="max-w-42 sm:max-w-none"
          style={{
            height: '32px',
            fontSize: '12px',
          }}
          placeholder="End Date"
        />
      </div>
    </div>
  );
};
