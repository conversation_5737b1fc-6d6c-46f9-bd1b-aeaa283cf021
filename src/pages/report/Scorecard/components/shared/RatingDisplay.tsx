import React from 'react';

interface RatingDisplayProps {
  platform: string;
  rating: number;
  maxRating: number;
  showLabel?: boolean;
  size?: 'small' | 'medium' | 'large';
  progressBarColor?: string;
}

const RatingDisplay: React.FC<RatingDisplayProps> = ({
  platform,
  rating,
  maxRating,
  showLabel = true,
  size = 'medium',
  progressBarColor = '#432890',
}) => {
  const safeRating =
    typeof rating === 'number' && isFinite(rating) && rating >= 0 ? rating : 0;
  const safeMaxRating =
    typeof maxRating === 'number' && isFinite(maxRating) && maxRating > 0
      ? maxRating
      : 5;
  const safePlatform =
    typeof platform === 'string' && platform.trim() ? platform.trim() : 'N/A';

  const clampedRating = Math.min(safeRating, safeMaxRating);

  const renderStars = () => {
    const fullStars = Math.max(0, Math.floor(clampedRating));
    const partialAmount = clampedRating - fullStars;
    const hasPartialStar = partialAmount > 0;
    const emptyStars = Math.max(0, 5 - fullStars - (hasPartialStar ? 1 : 0));

    const starSize = size === 'small' ? 16 : size === 'large' ? 24 : 20;

    const StarSVG = ({
      fillPercentage = 100,
      index,
    }: {
      fillPercentage?: number;
      index: number;
    }) => {
      const clipId = `starClip-${index}-${fillPercentage}`;

      return (
        <svg
          width={starSize}
          height={starSize}
          viewBox="0 0 24 24"
          className="inline-block"
        >
          <defs>
            <clipPath id={clipId}>
              <rect x="0" y="0" width={`${fillPercentage}%`} height="100%" />
            </clipPath>
          </defs>
          <path
            d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
            fill="#d1d5db"
          />
          <path
            d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
            fill="#fbbf24"
            clipPath={`url(#${clipId})`}
          />
        </svg>
      );
    };

    const totalStarWidth = 5 * starSize;

    return (
      <div
        className="flex items-center"
        style={{ width: `${totalStarWidth}px` }}
      >
        {Array(fullStars)
          .fill(0)
          .map((_, i) => (
            <StarSVG key={`full-${i}`} fillPercentage={100} index={i} />
          ))}
        {hasPartialStar && (
          <StarSVG
            key="partial"
            fillPercentage={partialAmount * 100}
            index={fullStars}
          />
        )}
        {Array(emptyStars)
          .fill(0)
          .map((_, i) => (
            <StarSVG
              key={`empty-${i}`}
              fillPercentage={0}
              index={fullStars + (hasPartialStar ? 1 : 0) + i}
            />
          ))}
      </div>
    );
  };

  const renderProgressBar = () => {
    const percentage =
      safeMaxRating > 0
        ? Math.min(100, Math.max(0, (clampedRating / safeMaxRating) * 100))
        : 0;
    const starSize = size === 'small' ? 16 : size === 'large' ? 24 : 20;
    const totalWidth = 5 * starSize;
    const barHeight =
      size === 'small' ? 'h-1.5' : size === 'large' ? 'h-2' : 'h-2';

    return (
      <div
        className={`bg-gray-300 rounded-full ${barHeight}`}
        style={{ width: `${Math.max(0, totalWidth)}px` }}
      >
        <div
          className={`${barHeight} rounded-full`}
          style={{
            width: `${Math.min(100, Math.max(0, percentage))}%`,
            backgroundColor: progressBarColor || '#432890',
          }}
        ></div>
      </div>
    );
  };

  const textSize =
    size === 'small'
      ? 'text-sm sm:text-base'
      : size === 'large'
        ? 'text-sm'
        : 'text-base sm:text-lg';

  return (
    <div className="grid grid-cols-[1fr_auto_auto] gap-2 items-center w-full px-4">
      {showLabel && (
        <span className={`font-medium text-gray-900 ${textSize} truncate`}>
          {safePlatform}
        </span>
      )}
      {showLabel && (
        <span className={`font-bold text-gray-900 ${textSize}`}>
          {clampedRating.toFixed(1)}
        </span>
      )}
      <div className="flex-shrink-0">
        {safeMaxRating === 5 ? renderStars() : renderProgressBar()}
      </div>
    </div>
  );
};

export default RatingDisplay;
