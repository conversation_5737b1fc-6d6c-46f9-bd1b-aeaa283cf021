import React from 'react';

export type SkeletonType =
  | 'property-info'
  | 'summary'
  | 'metrics-grid'
  | 'table'
  | 'metric-card';

interface LoadingSkeletonProps {
  type: SkeletonType;
  rows?: number;
  columns?: number;
  className?: string;
}

const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({
  type,
  rows = 3,
  columns = 4,
  className = '',
}) => {
  switch (type) {
    case 'property-info':
      return (
        <div className={`animate-pulse ${className}`}>
          {/* Property Name Skeleton */}
          <div className="h-6 bg-gray-300 mb-3 mx-2"></div>

          {/* Property Image Skeleton */}
          <div className="h-20 sm:h-24 bg-gray-300 mx-3 mb-3"></div>

          {/* Property Details Skeleton */}
          <div className="px-3 space-y-3">
            {[...Array(12)].map((_, index) => (
              <div key={index} className="flex justify-between items-center">
                <div className="h-3 bg-gray-300 w-1/3"></div>
                <div className="h-3 bg-gray-300 w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      );

    case 'summary':
      return (
        <div
          className={`bg-white shadow-sm overflow-hidden border border-gray-200 flex flex-col ${className}`}
        >
          <div
            className="px-2 sm:px-3 py-1 sm:py-2 flex-shrink-0"
            style={{ backgroundColor: '#8B8FE8' }}
          >
            <h3 className="text-xs sm:text-sm font-semibold text-white uppercase tracking-wide">
              Summary
            </h3>
          </div>
          <div className="p-2 sm:p-4 flex items-center justify-center flex-1 overflow-y-auto">
            <div className="text-center space-y-3 w-full">
              {[...Array(7)].map((_, index) => (
                <div
                  key={index}
                  className={`h-3 bg-gray-300 animate-pulse ${
                    index % 3 === 1
                      ? 'w-3/4 mx-auto'
                      : index % 3 === 2
                        ? 'w-5/6 mx-auto'
                        : index % 2 === 0
                          ? 'w-2/3 mx-auto'
                          : ''
                  }`}
                ></div>
              ))}
            </div>
          </div>
        </div>
      );

    case 'metrics-grid':
      return (
        <div className={`flex gap-1 sm:gap-2 ${className}`}>
          {[...Array(columns)].map((_, index) => (
            <div
              key={index}
              className="flex-1 py-1 sm:py-2 px-1 bg-white animate-pulse"
            >
              <div className="text-center flex flex-col justify-center px-1 h-full">
                <div className="h-6 bg-gray-300 mb-2"></div>
                <div className="h-3 bg-gray-300"></div>
              </div>
            </div>
          ))}
        </div>
      );

    case 'table':
      return (
        <div
          className={`bg-white shadow-sm overflow-hidden border border-gray-200 flex flex-col ${className}`}
        >
          <div className="overflow-y-auto flex-1">
            <table className="w-full">
              <thead
                style={{ backgroundColor: '#432890' }}
                className="sticky top-0"
              >
                <tr>
                  {[...Array(5)].map((_, index) => (
                    <th key={index} className="text-left px-2 py-2">
                      <div className="h-4 bg-gray-500 animate-pulse"></div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {[[...Array(rows)]].map((_, rowIndex) => (
                  <tr key={rowIndex} className="border-b border-gray-200">
                    {[...Array(5)].map((_, colIndex) => (
                      <td key={colIndex} className="px-2 py-3">
                        <div
                          className={`h-3 bg-gray-300 animate-pulse ${
                            colIndex > 0 ? 'w-16 mx-auto' : ''
                          }`}
                        ></div>
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      );

    case 'metric-card':
      return (
        <div
          className={`py-1 sm:py-2 px-1 bg-white animate-pulse ${className}`}
        >
          <div className="text-center flex flex-col justify-center px-1 h-full">
            <div className="h-6 bg-gray-300 mb-2"></div>
            <div className="h-3 bg-gray-300"></div>
          </div>
        </div>
      );

    default:
      return null;
  }
};

export default LoadingSkeleton;
