import React from 'react';
import { RootState } from '@/store';
import { useSelector } from 'react-redux';
import { getPerformanceScoreIcon } from '../utils/colorUtils';
import { getPerformanceTableHeaders } from '../utils/dateUtils';
import LoadingSkeleton from './shared/LoadingSkeleton';

const formatValue = (value: string): string => {
  if (value === 'N/A' || !value) return value;

  // Check if it's a percentage value
  if (value.includes('%')) {
    const numericValue = parseFloat(value.replace('%', ''));
    if (!isNaN(numericValue)) {
      return `${numericValue.toFixed(1)}%`;
    }
    return value;
  }

  // Check if it's a currency amount
  if (value.includes('$')) {
    const numericValue = parseFloat(value.replace(/[$,]/g, ''));
    if (!isNaN(numericValue)) {
      return `$${Math.round(numericValue).toLocaleString()}`;
    }
    return value;
  }

  // Check if it's a plain number
  const numericValue = parseFloat(value);
  if (!isNaN(numericValue)) {
    return Math.round(numericValue).toString();
  }

  return value;
};

const PerformanceTable: React.FC = () => {
  const { performanceRows, loadingStates, rawApiData, filters } = useSelector(
    (state: RootState) => state.scorecard,
  );
  const isLoading = loadingStates.performanceRows;

  const collectionsSection = performanceRows.slice(0, 3);
  const facilitiesSection = performanceRows.slice(3);

  const { table1Headers, table2Headers } = getPerformanceTableHeaders(
    rawApiData,
    filters.endDate,
  );

  const renderSectionHeader = (title: string) => (
    <tr>
      <td
        colSpan={5}
        className="px-2 py-1 text-sm font-medium text-white tracking-wide border-b"
        style={{ backgroundColor: '#8B8FE8' }}
      >
        {title}
      </td>
    </tr>
  );

  const renderRows = (
    rows: typeof performanceRows,
    startIndex: number = 0,
    headers: string[] = [],
  ) => (
    <>
      {rows.map((row, index) => {
        const scoreIcon = getPerformanceScoreIcon(row.category, row.actual);

        return (
          <tr
            key={startIndex + index}
            className="border-b border-gray-200 hover:bg-gray-50"
          >
            <td className="px-2 py-1 text-sm text-gray-900 leading-tight">
              {row.category}
            </td>
            <td className="px-2 py-1 text-sm text-gray-700 text-center leading-tight">
              {headers[index] || row.period}
            </td>
            <td className="px-2 py-1 text-sm text-gray-700 text-center leading-tight">
              {formatValue(row.target)}
            </td>
            <td className="px-2 py-1 text-sm text-gray-900 font-medium text-center leading-tight">
              {formatValue(row.actual)}
            </td>
            <td className="px-2 py-1 text-center text-lg">{scoreIcon}</td>
          </tr>
        );
      })}
    </>
  );

  if (isLoading) {
    return <LoadingSkeleton type="table" rows={8} />;
  }

  return (
    <div className="h-full flex flex-col bg-white shadow-sm overflow-hidden">
      <div className="flex-1 overflow-auto">
        <table className="w-full h-full">
          <thead
            style={{ backgroundColor: '#432890' }}
            className="sticky top-0 z-10"
          >
            <tr>
              <th className="text-left px-2 py-2 text-sm font-semibold tracking-wide text-white">
                Performance Category
              </th>
              <th className="text-center px-2 py-2 text-sm font-semibold tracking-wide text-white">
                Period
              </th>
              <th className="text-center px-2 py-2 text-sm font-semibold tracking-wide text-white">
                Target
              </th>
              <th className="text-center px-2 py-2 text-sm font-semibold tracking-wide text-white">
                Actual
              </th>
              <th className="text-center px-2 py-2 text-sm font-semibold tracking-wide text-white">
                Score
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {renderSectionHeader(
              'Collections, Bad Debts, Write Off & Recovery',
            )}
            {renderRows(collectionsSection, 0, table1Headers)}
            {renderSectionHeader('Capital & Facilities Maintenance')}
            {renderRows(
              facilitiesSection,
              collectionsSection.length,
              table2Headers,
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default PerformanceTable;
