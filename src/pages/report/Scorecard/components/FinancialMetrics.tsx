import React from 'react';
import { RootState } from '@/store';
import { useSelector } from 'react-redux';
import LoadingSkeleton from './shared/LoadingSkeleton';
import MetricCard from './shared/MetricCard';

const FinancialMetrics: React.FC = () => {
  const { financialMetrics, financialDateRange, loadingStates } = useSelector(
    (state: RootState) => state.scorecard,
  );
  const isLoading = loadingStates.financialMetrics;

  if (isLoading) {
    return (
      <div className="flex flex-col h-full">
        <div
          className="p-1 sm:p-2 rounded-sm flex-shrink-0"
          style={{ backgroundColor: '#8B8FE8' }}
        >
          <div className="h-4 bg-gray-300 rounded w-64 animate-pulse"></div>
        </div>
        <div className="mt-1 sm:mt-2 flex-1">
          <LoadingSkeleton type="metrics-grid" columns={6} className="h-full" />
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      <div
        className="p-1 sm:p-2 flex-shrink-0"
        style={{ backgroundColor: '#8B8FE8' }}
      >
        <h3 className="font-medium text-white text-sm sm:text-base">
          Financial Performance to YTD Budget {financialDateRange || 'N/A'}
        </h3>
      </div>
      <div className="flex gap-1 mt-1 sm:mt-2 flex-1">
        {financialMetrics.map((metric, index) => (
          <MetricCard
            key={index}
            value={metric.value}
            label={metric.label}
            color={metric.color}
            className="flex-1 bg-white h-full"
          />
        ))}
      </div>
    </div>
  );
};

export default FinancialMetrics;
