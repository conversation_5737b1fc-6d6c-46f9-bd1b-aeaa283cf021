import React from 'react';
import { RootState } from '@/store';
import { useSelector } from 'react-redux';
import RatingDisplay from './shared/RatingDisplay';

const ReputationSummary: React.FC = () => {
  const { reputationMetrics, loadingStates } = useSelector(
    (state: RootState) => state.scorecard,
  );
  const isLoading = loadingStates.reputationMetrics;

  if (isLoading) {
    return (
      <div className="flex flex-col h-full">
        <div
          className="p-1 sm:p-2 flex-shrink-0"
          style={{ backgroundColor: '#8B8FE8' }}
        >
          <div className="h-4 bg-gray-300 w-24 animate-pulse"></div>
        </div>
        <div className="py-1 sm:py-2 px-1 bg-white mt-1 sm:mt-2 flex-1 animate-pulse">
          <div className="text-center h-full flex flex-col justify-center">
            <div className="space-y-2">
              {[...Array(2)].map((_, index) => (
                <div key={index} className="flex items-center justify-center">
                  <div className="h-3 bg-gray-300 w-20"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      <div
        className="p-1 sm:p-2 flex-shrink-0"
        style={{ backgroundColor: '#8B8FE8' }}
      >
        <h3 className="font-medium text-white text-sm sm:text-base">
          Reputation Management
        </h3>
      </div>
      <div className="py-2 px-2 bg-white mt-1 sm:mt-2 flex-1">
        <div className="h-full flex flex-col justify-center">
          <div className="space-y-2">
            {reputationMetrics.map((metric, index) => (
              <div key={index} className="w-full">
                <RatingDisplay
                  platform={metric.platform}
                  rating={metric.rating}
                  maxRating={metric.maxRating}
                  size="large"
                />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReputationSummary;
