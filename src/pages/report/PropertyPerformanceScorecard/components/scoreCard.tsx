import { LocalFormatedDataType } from '@/pages/report/PropertyPerformanceScorecard/mokeData';
import { Skeleton } from '@/components/ui/skeleton';
import Styles from './performanceScoreCard1.module.css';

interface PerformanceScoreCardPropsTypes {
  item: LocalFormatedDataType;
  loading?: boolean;
  bgColor?: boolean;
}

export default function PerformanceScoreCard1(
  props: PerformanceScoreCardPropsTypes,
) {
  const { item, loading, bgColor = true } = props;
  return (
    <div
      className={`${Styles.scoreCard} ${bgColor ? 'bg-[#fff]' : 'bg-[inherit]'}`}
    >
      <div className="flex-1  text-center">
        <div className={`${Styles?.[item?.color]} ${Styles.scoreText}`}>
          {loading ? (
            <Skeleton className="h-[20px] w-[40%] m-auto" />
          ) : item?.score ? (
            item?.score
          ) : (
            'N/A'
          )}
        </div>
        <div className={` ${Styles.nameText}`}>{item?.name}</div>
      </div>
    </div>
  );
}
