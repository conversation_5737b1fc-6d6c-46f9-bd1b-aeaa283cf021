import React, { useEffect } from 'react';
import { getStatementOfOperationsPTD } from '@/api/statementOfOperationsApi';
import {
  PTDCategoryData,
  PTDCategoryTotals,
  PTDRevenueItem,
  setError,
  setLoading,
  setPTDReport,
} from '@/slice/incomeReportSlice';
import { RootState } from '@/store';
import { FileDown, FileUp, Search } from 'lucide-react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Container } from '@/components/common/container';
import ReportFilters from '../components/ReportFilters';
import { createPTDReportColumns } from '../components/ReportTable';
import { exportPTDToExcel, exportPTDToPDF } from '../utils/formatters';

interface PTDDataItem {
  ExpandTotal: string;
  Total: string;
  PTD_Actual: number;
  PTD_Budget: number;
  PTD_Variance: number;
  PTD_Variance_Perct: number;
  YTD_Actual: number;
  YTD_Budget: number;
  YTD_Variance: number;
  YTD_Variance_Perct: number;
}

const sortOrder = [
  'TOTAL REVENUE',
  'TOTAL EXPENSES',
  'NET INCOME',
  'ADD BACK:',
  'EBITDA',
  'EBITDA MARGIN',
  'ADD BACK: NON-RECURRING ITEMS',
  'ADJUSTED EBITDA',
  'ADJUSTED EBITDA MARGIN',
  'RETURN ON REVENUE',
  'RETURN ON REVENUE TOTAL',
  'RETURN ON REVENUE MARGIN',
  'RETURN ON REVENUE1',
];

const descriptionSortOrder = [
  'Salaries & Related',
  'Employee Benefits Expenses',
  'Property Management Fees',
  'Other PM Fees',
  'Other Service Revenue',
  'D&C PM Rehab',
  'Developer & Construction Fees',
  'Asset Management Fee',
  'Employee Benefits Revenue',
  'Other Revenue',
  'Advertising & Promotion',
  'Travel & Entertainment',
  'Automotive',
  'Rent & Related',
  'Office/Furniture/G&A/Mail/Phone',
  'Recruiting & Other Employment Related',
  'Resident Credit Verification',
  'Professional Fees',
  'Computer',
  'Insurance',
  'Project Related Expenses',
  'Transition Costs',
  'Other Expenses',
  'Interest/Taxes/Depreciation/Amortization',
];

const formatPTDData = (
  data: PTDDataItem[],
): {
  categoryData: PTDCategoryData[];
  lastMonth: string;
} => {
  if (!data || data.length === 0) {
    return {
      lastMonth: 'January 2025',
      categoryData: [],
    };
  }

  const categories = Array.from(
    new Set(data.map((item) => item.ExpandTotal)),
  ).filter((category) => category !== 'Units');

  const extendedCategories = [...categories];
  if (categories.includes('RETURN ON REVENUE')) {
    extendedCategories.push('RETURN ON REVENUE TOTAL');
  }

  const categoryDataMap = new Map<string, PTDCategoryData>();
  extendedCategories.forEach((category) => {
    const categoryItems =
      category === 'RETURN ON REVENUE'
        ? data.filter(
            (item) => item.ExpandTotal === category && item.Total !== 'TOTAL',
          )
        : category === 'RETURN ON REVENUE TOTAL'
          ? data.filter(
              (item) =>
                item.ExpandTotal === 'RETURN ON REVENUE' &&
                item.Total === 'TOTAL',
            )
          : data.filter(
              (item) => item.ExpandTotal === category && item.Total !== '',
            );

    const filteredItems =
      category === 'TOTAL REVENUE'
        ? categoryItems.filter((item) => item.Total !== 'Acquisition Fees')
        : categoryItems;

    const groupedItems = groupByCategory(filteredItems, category);
    const totalData = calculateTotals(filteredItems);

    categoryDataMap.set(category, {
      title: category,
      items: groupedItems,
      total: totalData,
    });
  });

  const categoryDataArray = Array.from(categoryDataMap.values());

  const totalRevenueCategory = categoryDataMap.get('TOTAL REVENUE');
  const ebitdaCategory = categoryDataMap.get('EBITDA');
  const adjustedEbitdaCategory = categoryDataMap.get('ADJUSTED EBITDA');
  const returnOnRevenueCategory = categoryDataMap.get(
    'RETURN ON REVENUE TOTAL',
  );

  const ebitdaMargin = calculateMarginCategory(
    ebitdaCategory,
    totalRevenueCategory,
    'EBITDA MARGIN',
  );
  if (ebitdaMargin) categoryDataArray.push(ebitdaMargin);

  const adjustedEbitdaMargin = calculateMarginCategory(
    adjustedEbitdaCategory,
    totalRevenueCategory,
    'ADJUSTED EBITDA MARGIN',
  );
  if (adjustedEbitdaMargin) categoryDataArray.push(adjustedEbitdaMargin);

  const returnOnRevenueMargin = calculateMarginCategory(
    returnOnRevenueCategory,
    totalRevenueCategory,
    'RETURN ON REVENUE MARGIN',
  );
  if (returnOnRevenueMargin) categoryDataArray.push(returnOnRevenueMargin);

  const lastMonth = data.length > 0 ? 'January 2025' : 'January 2025';

  return {
    lastMonth: lastMonth,
    categoryData: categoryDataArray,
  };
};

const groupByCategory = (
  data: PTDDataItem[],
  category?: string,
): PTDRevenueItem[] => {
  const categoriesMap: Map<string, PTDDataItem[]> = new Map();

  data.forEach((item) => {
    const groupKey =
      category === 'RETURN ON REVENUE' && item.Total === ''
        ? 'Total'
        : category === 'RETURN ON REVENUE TOTAL'
          ? 'Total'
          : item.Total;

    if (!categoriesMap.has(groupKey)) {
      categoriesMap.set(groupKey, []);
    }
    categoriesMap.get(groupKey)?.push(item);
  });

  const items = Array.from(categoriesMap.entries()).map(([name, items]) => {
    const ptdActual = sumByProperty(items, 'PTD_Actual');
    const ptdBudget = sumByProperty(items, 'PTD_Budget');
    const ptdVariance = sumByProperty(items, 'PTD_Variance');
    const ptdVariancePercent = sumByProperty(items, 'PTD_Variance_Perct');

    const ytdActual = sumByProperty(items, 'YTD_Actual');
    const ytdBudget = sumByProperty(items, 'YTD_Budget');
    const ytdVariance = sumByProperty(items, 'YTD_Variance');
    const ytdVariancePercent = sumByProperty(items, 'YTD_Variance_Perct');

    return {
      name,
      ptdActual,
      ptdBudget,
      ptdVariance,
      ptdVariancePercent,
      ytdActual,
      ytdBudget,
      ytdVariance,
      ytdVariancePercent,
    };
  });

  return items.sort((a, b) => {
    const indexA = descriptionSortOrder.indexOf(a.name);
    const indexB = descriptionSortOrder.indexOf(b.name);

    if (indexA !== -1 && indexB !== -1) {
      return indexA - indexB;
    }

    if (indexA !== -1) return -1;
    if (indexB !== -1) return 1;

    return a.name.localeCompare(b.name);
  });
};

const calculateTotals = (data: PTDDataItem[]): PTDCategoryTotals => {
  const ptdActual = sumByProperty(data, 'PTD_Actual');
  const ptdBudget = sumByProperty(data, 'PTD_Budget');
  const ptdVariance = sumByProperty(data, 'PTD_Variance');
  const ptdVariancePercent = sumByProperty(data, 'PTD_Variance_Perct');

  const ytdActual = sumByProperty(data, 'YTD_Actual');
  const ytdBudget = sumByProperty(data, 'YTD_Budget');
  const ytdVariance = sumByProperty(data, 'YTD_Variance');
  const ytdVariancePercent = sumByProperty(data, 'YTD_Variance_Perct');

  return {
    ptdActual,
    ptdBudget,
    ptdVariance,
    ptdVariancePercent,
    ytdActual,
    ytdBudget,
    ytdVariance,
    ytdVariancePercent,
  };
};

const sumByProperty = (items: PTDDataItem[], property: keyof PTDDataItem) => {
  return items.reduce(
    (sum, item) => sum + ((item[property] as number) || 0),
    0,
  );
};

const calculateMarginCategory = (
  numeratorCategory: PTDCategoryData | undefined,
  totalRevenueCategory: PTDCategoryData | undefined,
  marginTitle: string,
): PTDCategoryData | null => {
  if (!numeratorCategory || !totalRevenueCategory) return null;

  const numeratorTotal = numeratorCategory.total;
  const revenueTotal = totalRevenueCategory.total;

  const ptdActualMargin =
    revenueTotal.ptdActual !== 0
      ? (numeratorTotal.ptdActual / revenueTotal.ptdActual) * 100
      : 0;

  const ptdBudgetMargin =
    revenueTotal.ptdBudget !== 0
      ? (numeratorTotal.ptdBudget / revenueTotal.ptdBudget) * 100
      : 0;

  const ytdActualMargin =
    revenueTotal.ytdActual !== 0
      ? (numeratorTotal.ytdActual / revenueTotal.ytdActual) * 100
      : 0;

  const ytdBudgetMargin =
    revenueTotal.ytdBudget !== 0
      ? (numeratorTotal.ytdBudget / revenueTotal.ytdBudget) * 100
      : 0;

  const ptdVariance = ptdActualMargin - ptdBudgetMargin;
  const ytdVariance = ytdActualMargin - ytdBudgetMargin;

  return {
    title: marginTitle,
    items: [],
    total: {
      ptdActual: ptdActualMargin,
      ptdBudget: ptdBudgetMargin,
      ptdVariance,
      ptdVariancePercent:
        ptdBudgetMargin !== 0 ? (ptdVariance / ptdBudgetMargin) * 100 : 0,
      ytdActual: ytdActualMargin,
      ytdBudget: ytdBudgetMargin,
      ytdVariance,
      ytdVariancePercent:
        ytdBudgetMargin !== 0 ? (ytdVariance / ytdBudgetMargin) * 100 : 0,
    },
  };
};

const convertTotalForReportTable = (
  totals: PTDCategoryTotals,
  categoryTitle?: string,
): Record<string, number | string> => {
  const isMarginCategory =
    categoryTitle &&
    (categoryTitle === 'EBITDA MARGIN' ||
      categoryTitle === 'ADJUSTED EBITDA MARGIN' ||
      categoryTitle === 'RETURN ON REVENUE MARGIN');

  if (isMarginCategory) {
    return {
      ptdActual: `${totals.ptdActual.toFixed(2)}%`,
      ptdBudget: `${totals.ptdBudget.toFixed(2)}%`,
      ptdVariance: `${totals.ptdVariance.toFixed(2)}%`,
      ptdVariancePercent: totals.ptdVariancePercent,
      ytdActual: `${totals.ytdActual.toFixed(2)}%`,
      ytdBudget: `${totals.ytdBudget.toFixed(2)}%`,
      ytdVariance: `${totals.ytdVariance.toFixed(2)}%`,
      ytdVariancePercent: totals.ytdVariancePercent,
    };
  }

  return {
    ptdActual: totals.ptdActual,
    ptdBudget: totals.ptdBudget,
    ptdVariance: totals.ptdVariance,
    ptdVariancePercent: totals.ptdVariancePercent,
    ytdActual: totals.ytdActual,
    ytdBudget: totals.ytdBudget,
    ytdVariance: totals.ytdVariance,
    ytdVariancePercent: totals.ytdVariancePercent,
  };
};

const getMonthNumber = (monthName: string): string => {
  const months = {
    January: '1',
    February: '2',
    March: '3',
    April: '4',
    May: '5',
    June: '6',
    July: '7',
    August: '8',
    September: '9',
    October: '10',
    November: '11',
    December: '12',
  };

  return months[monthName as keyof typeof months] || '1';
};

const sortCategories = (categories: PTDCategoryData[]): PTDCategoryData[] => {
  return [...categories].sort((a, b) => {
    const indexA = sortOrder.indexOf(a.title);
    const indexB = sortOrder.indexOf(b.title);

    if (indexA !== -1 && indexB !== -1) {
      return indexA - indexB;
    }

    if (indexA !== -1) return -1;
    if (indexB !== -1) return 1;

    return 0;
  });
};

const StatementOfOperationsPTD: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const {
    ptdReport,
    lastClosedMonth,
    loading,
    error,
    expandedCategories,
    filters,
  } = useSelector((state: RootState) => ({
    ptdReport: state.incomeReport.ptdReport || [],
    lastClosedMonth: state.incomeReport.lastClosedMonth,
    loading: state.incomeReport.loading,
    error: state.incomeReport.error,
    expandedCategories: state.incomeReport.expandedCategories,
    filters: state.incomeReport.filters,
  }));

  useEffect(() => {
    const fetchPTDStatement = async () => {
      dispatch(setLoading(true));
      try {
        const monthNumbers =
          filters.month.length > 0
            ? filters.month.map((month) => getMonthNumber(month))
            : ['1'];

        const yearValue = filters.year || '2025';

        const requestBody = {
          year: yearValue,
          month: monthNumbers.length === 1 ? monthNumbers[0] : monthNumbers,
          department:
            filters.department.length === 0
              ? null
              : filters.department.length === 1
                ? filters.department[0]
                : filters.department,
          businessType:
            filters.businessType.length === 0
              ? null
              : filters.businessType.length === 1
                ? filters.businessType[0]
                : filters.businessType,
          marketleader:
            filters.marketLeader.length === 0
              ? null
              : filters.marketLeader.length === 1
                ? filters.marketLeader[0]
                : filters.marketLeader,
          adminBu:
            filters.adminBU.length === 0
              ? null
              : filters.adminBU.length === 1
                ? filters.adminBU[0]
                : filters.adminBU,
        };

        const response = await getStatementOfOperationsPTD(requestBody);

        if (response) {
          if (!response.data || response.data.length === 0) {
            toast.warning('No data found for the selected filters');
          }
          const formattedData = formatPTDData(response.data);
          dispatch(setPTDReport(formattedData));
        }
      } catch (error) {
        console.error('Error in fetching PTD statement:', error);
        dispatch(setError('Failed to fetch PTD statement data'));
      } finally {
        dispatch(setLoading(false));
      }
    };

    fetchPTDStatement();
  }, [filters, dispatch]);

  const handleExportToExcel = () => {
    const sortedCategoryData = sortCategories(ptdReport);
    exportPTDToExcel(
      sortedCategoryData,
      lastClosedMonth,
      expandedCategories,
      filters,
    );
  };

  const handleExportToPDF = () => {
    const sortedCategoryData = sortCategories(ptdReport);
    exportPTDToPDF(
      sortedCategoryData,
      lastClosedMonth,
      expandedCategories,
      filters,
    );
  };

  const handleDrillThrough = () => {
    navigate('/ptd-drill-through');
  };

  if (loading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="text-center">
          <div className="animate-spin h-8 w-8 border-4 border-[#43298F] border-t-transparent rounded-full mx-auto mb-4"></div>
          <p>Loading PTD report data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="text-center text-red-600">
          <p>Error: {error}</p>
        </div>
      </div>
    );
  }

  const ptdReportColumns = createPTDReportColumns();

  const sortedCategoryData = sortCategories(ptdReport);

  return (
    <Container title="Statement of Operations PTD" width="fluid">
      <div className="bg-white shadow-md rounded-lg p-4">
        <div className="mb-6">
          <ReportFilters />
        </div>

        <div className="overflow-x-auto bg-[#F4F4FF] rounded-lg">
          <div className="min-w-[1024px]">
            <div className="bg-[#F4F4FF] p-4 mb-4 rounded-t-lg flex justify-between items-center">
              <h3 className="text-lg font-semibold mb-2 text-[#43298F]">
                Period :{' '}
                {filters.month.length === 1
                  ? filters.month[0]
                  : filters.month.length > 1
                    ? `${filters.month[0]} - ${filters.month[filters.month.length - 1]}`
                    : 'January'}{' '}
                {filters.year}
              </h3>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  className="bg-white text-[#43298F] border-[#43298F] hover:bg-[#DEEFFF]"
                  onClick={handleDrillThrough}
                  disabled={sortedCategoryData.length === 0}
                >
                  <Search className="mr-2 h-4 w-4" />
                  Drill Through
                </Button>
                <Button
                  variant="outline"
                  className="bg-white text-[#43298F] border-[#43298F] hover:bg-[#DEEFFF]"
                  onClick={handleExportToExcel}
                  disabled={sortedCategoryData.length === 0}
                >
                  <FileUp className="mr-2 h-4 w-4" />
                  Export Excel
                </Button>
                <Button
                  variant="outline"
                  className="bg-white text-[#43298F] border-[#43298F] hover:bg-[#DEEFFF]"
                  onClick={handleExportToPDF}
                  disabled={sortedCategoryData.length === 0}
                >
                  <FileDown className="mr-2 h-4 w-4" />
                  Export PDF
                </Button>
              </div>
            </div>

            <div className="p-4">
              {sortedCategoryData.length > 0 ? (
                <div className="border border-[#F3F4FF] overflow-hidden">
                  <table className="min-w-full border-collapse">
                    <thead>
                      {/* Grouped Headers Row */}
                      <tr className="text-white">
                        <th className="border-[#F3F4FF]">
                          {/* Empty cell for Description column */}
                        </th>
                        <th
                          colSpan={4}
                          className="p-3 text-sm font-medium uppercase tracking-wider border border-black text-center bg-[#ffffff] text-black font-bold"
                          style={{
                            borderLeft: '2px solid black',
                            borderRight: '2px solid black',
                          }}
                        >
                          Period To Date {filters?.year || '2025'}
                        </th>
                        <th
                          colSpan={4}
                          className="p-3 text-sm font-medium uppercase tracking-wider border border-black text-center bg-[#ffffff] text-black font-bold"
                          style={{
                            borderLeft: '2px solid black',
                            borderRight: '2px solid black',
                          }}
                        >
                          Year To Date {filters?.year || '2025'}
                        </th>
                      </tr>
                      {/* Column Headers Row */}
                      <tr className="bg-[#43298F] text-white">
                        {ptdReportColumns.map((column, index) => (
                          <th
                            key={column.id}
                            className={`p-3 text-xs font-medium uppercase tracking-wider border border-black ${
                              column.id !== 'name' &&
                              column.id !== 'property' &&
                              column.id !== 'propertyAlias' &&
                              column.id !== 'rpm'
                                ? 'text-right'
                                : 'text-left'
                            } ${column.className || ''}`}
                            style={
                              index === 1
                                ? { borderLeft: '2px solid black' }
                                : index === 4
                                  ? { borderRight: '2px solid black' }
                                  : index === 5
                                    ? { borderLeft: '2px solid black' }
                                    : index === 8
                                      ? { borderRight: '2px solid black' }
                                      : {}
                            }
                          >
                            {column.header}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody className="bg-white">
                      {sortedCategoryData.map(
                        (category: PTDCategoryData, categoryIndex: number) => (
                          <React.Fragment key={category.title}>
                            {/* Add section spacing and section headers */}
                            {categoryIndex > 0 && (
                              <tr>
                                <td className="h-4 bg-white border-none"></td>
                                <td
                                  className="h-4 bg-white border-none"
                                  style={{ borderLeft: '2px solid black' }}
                                ></td>
                                <td className="h-4 bg-white border-none"></td>
                                <td className="h-4 bg-white border-none"></td>
                                <td
                                  className="h-4 bg-white border-none"
                                  style={{ borderRight: '2px solid black' }}
                                ></td>
                                <td
                                  className="h-4 bg-white border-none"
                                  style={{ borderLeft: '2px solid black' }}
                                ></td>
                                <td className="h-4 bg-white border-none"></td>
                                <td className="h-4 bg-white border-none"></td>
                                <td
                                  className="h-4 bg-white border-none"
                                  style={{ borderRight: '2px solid black' }}
                                ></td>
                              </tr>
                            )}

                            {/* Add section headers like REVENUE, EXPENSES, etc. */}
                            {(category.title === 'TOTAL REVENUE' ||
                              category.title === 'TOTAL EXPENSES') && (
                              <tr className="bg-[#ffffff] text-black">
                                <td className="px-3 py-2 font-bold text-left">
                                  {category.title === 'TOTAL REVENUE'
                                    ? 'REVENUE'
                                    : category.title === 'TOTAL EXPENSES'
                                      ? 'EXPENSES'
                                      : ''}
                                </td>
                                <td
                                  className="px-3 py-2"
                                  style={{ borderLeft: '2px solid black' }}
                                ></td>
                                <td className="px-3 py-2"></td>
                                <td className="px-3 py-2"></td>
                                <td
                                  className="px-3 py-2"
                                  style={{ borderRight: '2px solid black' }}
                                ></td>
                                <td
                                  className="px-3 py-2"
                                  style={{ borderLeft: '2px solid black' }}
                                ></td>
                                <td className="px-3 py-2"></td>
                                <td className="px-3 py-2"></td>
                                <td
                                  className="px-3 py-2"
                                  style={{ borderRight: '2px solid black' }}
                                ></td>
                              </tr>
                            )}

                            {expandedCategories[category.title] &&
                              category.items.map((item, itemIndex) => {
                                return (
                                  <tr
                                    key={`${categoryIndex}-${itemIndex}`}
                                    className={`border-b border-[#DEEFFF] ${
                                      itemIndex % 2 === 1
                                        ? 'bg-[#F4F4FF]'
                                        : 'bg-white'
                                    }`}
                                  >
                                    {ptdReportColumns.map(
                                      (column, colIndex) => (
                                        <td
                                          key={`${categoryIndex}-${itemIndex}-${column.id}`}
                                          className={`px-2 py-0 whitespace-nowrap border-r border-[#DEEFFF] last:border-r-0 ${
                                            column.id !== 'name' &&
                                            column.id !== 'property' &&
                                            column.id !== 'propertyAlias' &&
                                            column.id !== 'rpm'
                                              ? 'text-right'
                                              : 'text-left'
                                          } ${column.className || ''}`}
                                          style={
                                            colIndex === 1
                                              ? {
                                                  borderLeft: '2px solid black',
                                                }
                                              : colIndex === 4
                                                ? {
                                                    borderRight:
                                                      '2px solid black',
                                                  }
                                                : colIndex === 5
                                                  ? {
                                                      borderLeft:
                                                        '2px solid black',
                                                    }
                                                  : colIndex === 8
                                                    ? {
                                                        borderRight:
                                                          '2px solid black',
                                                      }
                                                    : {}
                                          }
                                        >
                                          {column.accessor(item)}
                                        </td>
                                      ),
                                    )}
                                  </tr>
                                );
                              })}

                            {category.title !== 'RETURN ON REVENUE' && (
                              <tr
                                className={`font-bold text-[#43298F] border-b border-[#DEEFFF] ${
                                  [
                                    'EBITDA MARGIN',
                                    'ADD BACK: NON-RECURRING ITEMS',
                                    'ADJUSTED EBITDA MARGIN',
                                  ].includes(category.title)
                                    ? 'bg-white'
                                    : 'bg-[#DEEFFF]'
                                }`}
                              >
                                <td
                                  colSpan={
                                    ptdReportColumns.filter(
                                      (col) =>
                                        col.id === 'name' ||
                                        col.id === 'property' ||
                                        col.id === 'propertyAlias' ||
                                        col.id === 'rpm',
                                    ).length
                                  }
                                  className="px-2 py-0 whitespace-nowrap text-left font-bold border-r border-[#c7d2fe]"
                                  onClick={() => {}}
                                >
                                  <div className="flex items-center">
                                    {category.title}
                                  </div>
                                </td>
                                {ptdReportColumns
                                  .filter(
                                    (col) =>
                                      col.id !== 'name' &&
                                      col.id !== 'property' &&
                                      col.id !== 'propertyAlias' &&
                                      col.id !== 'rpm',
                                  )
                                  .map((column, index, array) => {
                                    const totalData =
                                      convertTotalForReportTable(
                                        category.total,
                                        category.title,
                                      );
                                    // Get the actual column index in the full table
                                    const fullColumnIndex =
                                      ptdReportColumns.findIndex(
                                        (col) => col.id === column.id,
                                      );
                                    return (
                                      <td
                                        key={`total-${categoryIndex}-${column.id}`}
                                        className={`px-2 py-0 whitespace-nowrap text-right font-bold ${index < array.length - 1 ? 'border-r border-[#c7d2fe]' : ''}`}
                                        style={
                                          fullColumnIndex === 1
                                            ? { borderLeft: '2px solid black' }
                                            : fullColumnIndex === 4
                                              ? {
                                                  borderRight:
                                                    '2px solid black',
                                                }
                                              : fullColumnIndex === 5
                                                ? {
                                                    borderLeft:
                                                      '2px solid black',
                                                  }
                                                : fullColumnIndex === 8
                                                  ? {
                                                      borderRight:
                                                        '2px solid black',
                                                    }
                                                  : {}
                                        }
                                      >
                                        {(() => {
                                          const value = totalData[column.id];
                                          if (typeof value !== 'number')
                                            return value ?? '';
                                          if (
                                            column.id
                                              .toLowerCase()
                                              .includes('percent')
                                          ) {
                                            return `${value.toLocaleString('en-US', { maximumFractionDigits: 1 })}%`;
                                          }

                                          const absValue = Math.abs(value);
                                          const formatted =
                                            absValue.toLocaleString('en-US', {
                                              maximumFractionDigits: 0,
                                            });
                                          return value < 0
                                            ? `(${formatted})`
                                            : formatted;
                                        })()}
                                      </td>
                                    );
                                  })}
                              </tr>
                            )}
                          </React.Fragment>
                        ),
                      )}
                    </tbody>
                    <tbody>
                      <tr>
                        <td className="h-0 border-none"></td>
                        <td
                          className="h-0 border-none"
                          style={{ borderBottom: '2px solid black' }}
                        ></td>
                        <td
                          className="h-0 border-none"
                          style={{ borderBottom: '2px solid black' }}
                        ></td>
                        <td
                          className="h-0 border-none"
                          style={{ borderBottom: '2px solid black' }}
                        ></td>
                        <td
                          className="h-0 border-none"
                          style={{ borderBottom: '2px solid black' }}
                        ></td>
                        <td
                          className="h-0 border-none"
                          style={{ borderBottom: '2px solid black' }}
                        ></td>
                        <td
                          className="h-0 border-none"
                          style={{ borderBottom: '2px solid black' }}
                        ></td>
                        <td
                          className="h-0 border-none"
                          style={{ borderBottom: '2px solid black' }}
                        ></td>
                        <td
                          className="h-0 border-none"
                          style={{ borderBottom: '2px solid black' }}
                        ></td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center p-8 text-gray-500">
                  No data available for the selected filters
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Container>
  );
};

export default StatementOfOperationsPTD;
