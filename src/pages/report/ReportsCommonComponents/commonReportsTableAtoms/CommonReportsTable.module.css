.table1 {
  /* @apply min-w-full border-collapse; */
  @apply border-collapse;
  /* table-layout: fixed;
  width: 100%; */
}
.groupHeadingRow {
  height: 40px;
}

.table1 td,
th {
  padding: 2px 6px;
}

.bodyCell {
  border-left: 1px solid #deefff;
  font-weight: 400;
  font-size: 15px;
}

.headingMergeCell {
  /* background-color: #fff; */
  border: 2px solid black;
}

.headingCell {
  font-weight: 500;
  font-size: 15px;
  /* text-align: end; */
}

.subHeadingCell {
  background-color: #43298f;
  color: #fff;
  border-right: 1px solid black;
}

.borderLeft {
  border-left: 2px solid black;
}

.borderRight {
  border-right: 2px solid black;
}

.bodyRow {
  text-align: end;
}

.bodyRow:nth-child(even) {
  /* background: #fff;
  border: 1px solid #deefff; */

  background: #f4f4ff;
}
.bodyRow:nth-child(odd) {
  /* background: #f4f4ff; */

  background: #fff;
  border: 1px solid #deefff;
}

.lastRowBorderBtm:last-child td:not(:first-child),
.lastRowBorderBtm:last-child th:not(:first-child) {
  border-bottom: 2px solid #000;
}

.lastRowFirstCellBorderBtm:last-child th:nth-child(1),
.lastRowFirstCellBorderBtm:last-child td:nth-child(1) {
  border-bottom: 2px solid #000;
}
