import { HTMLAttributes, ReactNode } from 'react';
import Styles from './CommonReportsTable.module.css';

type PropsType = {
  children: ReactNode;
  className?: string;
} & HTMLAttributes<HTMLTableElement>;

type RowPropsType = {
  children: ReactNode;
  className?: string;
} & HTMLAttributes<HTMLTableRowElement>;

interface BodyRowPropsTypes extends RowPropsType {
  borderBtmFirstColum?: boolean;
  lastRowBorder?: boolean;
}

type MergeCellProps = {
  children: ReactNode;
  colSpan?: number;
  className?: string;
} & HTMLAttributes<HTMLTableCellElement>;

type CellPropsTypes = {
  children: ReactNode;
  borderRight?: boolean;
  borderLeft?: boolean;
  className?: string;
  subHeading?: boolean;
  textAlign?: 'text-start' | 'text-end' | 'text-center';
} & HTMLAttributes<HTMLTableCellElement>;

const CommonTable = ({ children, className, ...rest }: PropsType) => (
  <table className={`${Styles.table1} ${className ?? ''}`} {...rest}>
    {children}
  </table>
);

const CommonTableMainHeaderRow = ({
  children,
  className = '',
  height = 'h-10',
}: RowPropsType & { height?: string }) => {
  return <tr className={`${height} ${className}`}>{children}</tr>;
};

const CommonTableSubHeaderRow = ({ children, className }: RowPropsType) => {
  return <tr className={` ${className}`}>{children}</tr>;
};

const CommonTableBodyRow = ({
  children,
  borderBtmFirstColum = false,
  lastRowBorder = true,
  ...rest
}: BodyRowPropsTypes) => {
  return (
    <tr
      className={`${Styles.bodyRow} 
      ${lastRowBorder ? Styles.lastRowBorderBtm : ''} 
      ${lastRowBorder && borderBtmFirstColum ? Styles.lastRowFirstCellBorderBtm : ''}
      `}
      {...rest}
    >
      {children}
    </tr>
  );
};

const CommonTableBodyRowTotal = ({
  children,
  className,
  borderBtmFirstColum = false,
  lastRowBorder = true,
}: BodyRowPropsTypes) => {
  return (
    <tr
      className={`bg-[#DEEFFF] text-[#43298F] 
        ${className} 
       ${lastRowBorder ? Styles.lastRowBorderBtm : ''} 
      ${lastRowBorder && borderBtmFirstColum ? Styles.lastRowFirstCellBorderBtm : ''}
        `}
    >
      {children}
    </tr>
  );
};

const CommonTableHeadingMergeCell = ({
  colSpan,
  children,
  className,
  bg = 'bg-white',
}: MergeCellProps & { bg?: string }) => {
  return (
    <th
      colSpan={colSpan}
      className={`${Styles.headingMergeCell} ${bg} ${className}`}
    >
      {children}
    </th>
  );
};

const CommonTableHeadingCell = ({
  children,
  borderRight,
  borderLeft,
  subHeading = true,
  textAlign = 'text-end',
  className,
}: CellPropsTypes) => {
  return (
    <th
      className={`${Styles.headingCell} ${subHeading ? Styles.subHeadingCell : ''}
      ${borderRight ? Styles.borderRight : ''} 
      ${borderLeft ? Styles.borderLeft : ''} ${textAlign} ${className}`}
    >
      {children}
    </th>
  );
};

const CommonTableBodyCell = ({
  children,
  borderRight,
  borderLeft,
  className,
  textAlign = 'text-end',
}: CellPropsTypes) => {
  return (
    <td
      className={`${Styles.bodyCell} ${textAlign} ${className}
      ${borderRight ? Styles.borderRight : ''} 
      ${borderLeft ? Styles.borderLeft : ''}`}
    >
      {children}
    </td>
  );
};

const CommonTableBodyTotalCell = ({
  children,
  borderRight,
  borderLeft,
  className,
  textAlign = 'text-end',
}: CellPropsTypes) => {
  return (
    <td
      className={`bg-[#DEEFFF]' font-bold ${textAlign} ${className}
      ${borderRight ? Styles.borderRight : ''} 
      ${borderLeft ? Styles.borderLeft : ''}`}
    >
      {children}
    </td>
  );
};

export {
  CommonTable,
  CommonTableMainHeaderRow,
  CommonTableSubHeaderRow,
  CommonTableBodyRowTotal,
  CommonTableHeadingMergeCell,
  CommonTableHeadingCell,
  CommonTableBodyRow,
  CommonTableBodyCell,
  CommonTableBodyTotalCell,
};
