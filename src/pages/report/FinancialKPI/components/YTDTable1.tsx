import { FinanceKPIRegionActualResponse } from '@/api/financeKPIRegionApi/financeKPIRegionApi.types';
import {
  CommonTable,
  CommonTableBodyCell,
  CommonTableBodyRow,
  CommonTableHeadingCell,
  CommonTableHeadingMergeCell,
  CommonTableMainHeaderRow,
  CommonTableSubHeaderRow,
} from '../../ReportsCommonComponents/commonReportsTableAtoms/CommonReportsTable';
import { ReportRowNoData } from '../../ReportsCommonComponents/ReportRowNoData';
import { formatValue } from '../utils/helperFinanceKpi';

interface PropsTypes {
  actualData: FinanceKPIRegionActualResponse[];
  tableHeading1: string;
  tableHeading2: string;
}

export default function YTDTable1(props: PropsTypes) {
  const { tableHeading1, tableHeading2, actualData } = props;
  return (
    <div>
      <CommonTable style={{ wordWrap: 'break-word' }}>
        <thead>
          <CommonTableMainHeaderRow>
            <th
              style={{ width: '100px', wordWrap: 'break-word' }}
              className="w-[100px]"
            ></th>
            <CommonTableHeadingMergeCell colSpan={6}>
              {tableHeading1}
            </CommonTableHeadingMergeCell>
            <CommonTableHeadingMergeCell colSpan={6}>
              {tableHeading2}
            </CommonTableHeadingMergeCell>
            <CommonTableHeadingMergeCell colSpan={6}>
              Variance
            </CommonTableHeadingMergeCell>
          </CommonTableMainHeaderRow>
          <CommonTableSubHeaderRow>
            <th></th>
            {/* Actual */}
            <CommonTableHeadingCell borderLeft className="w-[100px]">
              PM Fees
            </CommonTableHeadingCell>
            <CommonTableHeadingCell className="w-[100px]">
              Return-on Rev.
            </CommonTableHeadingCell>
            <CommonTableHeadingCell className="w-[100px]">
              Fee/ Unit
            </CommonTableHeadingCell>
            <CommonTableHeadingCell className="w-[100px]">
              Cost/ Door
            </CommonTableHeadingCell>
            <CommonTableHeadingCell className="w-[100px]">
              WAU
            </CommonTableHeadingCell>
            <CommonTableHeadingCell className="w-[100px]">
              Actual Units
            </CommonTableHeadingCell>

            {/* Budget */}
            <CommonTableHeadingCell className="w-[100px]">
              PM Fees
            </CommonTableHeadingCell>
            <CommonTableHeadingCell className="w-[100px]">
              Return-on Rev.
            </CommonTableHeadingCell>
            <CommonTableHeadingCell className="w-[100px]">
              Fee/ Unit
            </CommonTableHeadingCell>
            <CommonTableHeadingCell className="w-[100px]">
              Cost/ Door
            </CommonTableHeadingCell>
            <CommonTableHeadingCell className="w-[100px]">
              WAU
            </CommonTableHeadingCell>
            <CommonTableHeadingCell className="w-[100px]">
              Actual Units
            </CommonTableHeadingCell>

            {/* Variance */}
            <CommonTableHeadingCell className="w-[100px]">
              PM Fees
            </CommonTableHeadingCell>
            <CommonTableHeadingCell className="w-[100px]">
              Return-on Rev.
            </CommonTableHeadingCell>
            <CommonTableHeadingCell className="w-[100px]">
              Fee/ Unit
            </CommonTableHeadingCell>
            <CommonTableHeadingCell className="w-[100px]">
              Cost/ Door
            </CommonTableHeadingCell>
            <CommonTableHeadingCell className="w-[100px]">
              WAU
            </CommonTableHeadingCell>
            <CommonTableHeadingCell className="w-[100px]">
              Actual Units
            </CommonTableHeadingCell>
          </CommonTableSubHeaderRow>
        </thead>
        <tbody>
          {actualData?.length === 0 && <ReportRowNoData colSpan={18} />}

          {actualData?.map((item) => {
            return (
              <CommonTableBodyRow
                key={item?.regionmarket}
                // key={i}
              >
                <CommonTableBodyCell
                  textAlign="text-start"
                  borderRight
                  style={{ width: '100px', wordWrap: 'break-word' }}
                  className="w-[100px]"
                >
                  {item?.regionmarket}
                </CommonTableBodyCell>

                {/* Actual values */}
                <CommonTableBodyCell className="w-[100px]">
                  {formatValue(item?.['PM Fees_Actual'], '$')}
                </CommonTableBodyCell>
                <CommonTableBodyCell className="w-[100px]">
                  {formatValue(item?.['ROR_Actual'], '%')}
                </CommonTableBodyCell>
                <CommonTableBodyCell className="w-[100px]">
                  {formatValue(item?.['Fees/Unit_Actual'], '$')}
                </CommonTableBodyCell>
                <CommonTableBodyCell className="w-[100px]">
                  {formatValue(item?.['Cost/Unit_Actual'], '$')}
                </CommonTableBodyCell>
                <CommonTableBodyCell className="w-[100px]">
                  {formatValue(item?.['WAU_Actual'], '')}
                </CommonTableBodyCell>
                <CommonTableBodyCell borderRight className="w-[100px]">
                  {formatValue(item?.['Actual Units_Actual'], '')}
                </CommonTableBodyCell>

                {/* Budget Values */}
                <CommonTableBodyCell className="w-[100px]">
                  {formatValue(item?.['PM Fees_Budget'], '$')}
                </CommonTableBodyCell>
                <CommonTableBodyCell className="w-[100px]">
                  {formatValue(item?.['ROR_Budget'], '%')}
                </CommonTableBodyCell>
                <CommonTableBodyCell className="w-[100px]">
                  {formatValue(item?.['Fees/Unit_Budget'], '$')}
                </CommonTableBodyCell>
                <CommonTableBodyCell className="w-[100px]">
                  {formatValue(item?.['Cost/Unit_Budget'], '$')}
                </CommonTableBodyCell>
                <CommonTableBodyCell className="w-[100px]">
                  {formatValue(item?.['WAU_Budget'], '')}
                </CommonTableBodyCell>
                <CommonTableBodyCell borderRight className="w-[100px]">
                  {formatValue(item?.['Actual Units_Budget'], '')}
                </CommonTableBodyCell>

                {/*Variance*/}
                <CommonTableBodyCell className="w-[100px]">
                  {formatValue(item?.['PM Fees_Varriance'], '$')}
                </CommonTableBodyCell>
                <CommonTableBodyCell className="w-[100px]">
                  {formatValue(item?.['ROR_Varriance'], '%')}
                </CommonTableBodyCell>
                <CommonTableBodyCell className="w-[100px]">
                  {formatValue(item?.['Fees/Unit_Varriance'], '$')}
                </CommonTableBodyCell>
                <CommonTableBodyCell className="w-[100px]">
                  {formatValue(item?.['Cost/Unit_Varriance'], '$')}
                </CommonTableBodyCell>
                <CommonTableBodyCell className="w-[100px]">
                  {formatValue(item?.['WAU_Varriance'], '')}
                </CommonTableBodyCell>
                <CommonTableBodyCell borderRight className="w-[100px]">
                  {formatValue(item?.['Actual Units_Varriance'], '')}
                </CommonTableBodyCell>
              </CommonTableBodyRow>
            );
          })}
        </tbody>
      </CommonTable>

      {/*  */}
    </div>
  );
}
