import { useMemo } from 'react';
import {
  FinanceKPIRegionActualResponse,
  FinanceKPIRegionForecastResponse,
} from '@/api/financeKPIRegionApi/financeKPIRegionApi.types';
import { ReportFilters } from '@/slice/incomeReportSlice';
import { FileUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import YTDTable1 from '../components/YTDTable1';
import { downloadExcelMarketRegionActualAndExpected } from '../utils/exportDownloadFormatters';

interface PropsTypes {
  actualData: FinanceKPIRegionActualResponse[];
  forecastData: FinanceKPIRegionForecastResponse[];
  filters: ReportFilters;
  datePeriod: string;
}
export default function MarketSplitTable(props: PropsTypes) {
  const { actualData, forecastData, filters, datePeriod } = props;

  const formatedData = useMemo(() => {
    return forecastData?.map((item) => {
      return {
        // ...item,
        regionmarket: item?.regionmarket,

        // PM Fees
        'PM Fees_Actual': item?.['PM Fees_Forecast'],
        'PM Fees_Budget': item?.['PM Fees_Budget'],
        'PM Fees_Varriance': item?.['PM Fees_Variance'],
        'PM Fees_Variance_Pert': item?.['PM Fees_FY_Variance_Pert'],

        // ROR
        ROR_Actual: item?.['ROR_Forecast'],
        ROR_Budget: item?.['ROR_Budget'],
        ROR_Varriance: item?.['ROR_Variance'],
        ROR_Variance_Pert: item?.['ROR_FY_Variance_Pert'],

        // Fees/Unit
        'Fees/Unit_Actual': item?.['Fees/Unit_Forecast'],
        'Fees/Unit_Budget': item?.['Fees/Unit_Budget'],
        'Fees/Unit_Varriance': item?.['Fees/Unit_Variance'],
        'Fees/Unit_Variance_Pert': item?.['Fees/Unit_FY_Variance_Pert'],

        // Cost/Unit
        'Cost/Unit_Actual': item?.['Cost/Unit_Forecast'],
        'Cost/Unit_Budget': item?.['Cost/Unit_Budget'],
        'Cost/Unit_Varriance': item?.['Cost/Unit_Variance'],
        'Cost/Unit_Variance_Pert': item?.['Cost/Unit_FY_Variance_Pert'],

        // WAU
        WAU_Actual: item?.['WAU_Forecast'],
        WAU_Budget: item?.['WAU_Budget'],
        WAU_Varriance: item?.['WAU_Variance'],
        WAU_Variance_Pert: item?.['WAU_FY_Variance_Pert'],

        // Actual Units
        'Actual Units_Actual': item?.['Actual Units_Forecast'],
        'Actual Units_Budget': item?.['Actual Units_Budget'],
        'Actual Units_Varriance': item?.['Actual Units_Variance'],
        'Actual Units_Variance_Pert': item?.['Actual Units_FY_Variance_Pert'],
      };
    });
  }, [forecastData]);

  return (
    <>
      <div className="mb-4 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-[#43298F]">
          Period : {datePeriod}
        </h3>
        <Button
          variant="outline"
          className="bg-white text-[#43298F] border-[#43298F] hover:bg-[#DEEFFF] "
          onClick={() =>
            downloadExcelMarketRegionActualAndExpected({
              actualData: actualData,
              forecastData: forecastData,
              filters: filters,
            })
          }
        >
          <FileUp className="mr-2 h-4 w-4" />
          Export Excel
        </Button>
      </div>

      <div className="mb-[20px] ">
        <YTDTable1
          tableHeading1="YTD Actuals"
          tableHeading2="YTD Budget"
          actualData={actualData}
        />
      </div>

      <div className="my-[40px]">
        <YTDTable1
          tableHeading1="Full Year Expected"
          tableHeading2="Full Year Budget"
          actualData={formatedData}
        />
      </div>
    </>
  );
}
