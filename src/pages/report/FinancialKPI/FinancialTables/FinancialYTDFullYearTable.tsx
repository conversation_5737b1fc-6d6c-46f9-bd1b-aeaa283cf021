import { FinanceKPIRegionWiseResponce } from '@/api/financeKPIRegionApi/financeKPIRegionApi.types';
import { ReportFilters } from '@/slice/incomeReportSlice';
import { FileUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  CommonTable,
  CommonTableBodyCell,
  CommonTableBodyRow,
  CommonTableHeadingCell,
  CommonTableHeadingMergeCell,
  CommonTableMainHeaderRow,
  CommonTableSubHeaderRow,
} from '../../ReportsCommonComponents/commonReportsTableAtoms/CommonReportsTable';
import { ReportRowNoData } from '../../ReportsCommonComponents/ReportRowNoData';
import { downloadExcelYTDFullYear } from '../utils/exportDownloadFormatters';
import {
  formatValue,
  regionSplitRowOrderTable,
} from '../utils/helperFinanceKpi';

interface PropsTypes {
  summaryData: FinanceKPIRegionWiseResponce[];
  filters: ReportFilters;
  datePeriod: string;
}

export default function FinancialYTDFullYearTable(props: PropsTypes) {
  const { summaryData, filters, datePeriod } = props;

  return (
    <>
      <div className="mb-4 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-[#43298F]">
          Period : {datePeriod}
        </h3>
        <Button
          variant="outline"
          className="bg-white text-[#43298F] border-[#43298F] hover:bg-[#DEEFFF] "
          onClick={() => downloadExcelYTDFullYear(summaryData, filters)}
        >
          <FileUp className="mr-2 h-4 w-4" />
          Export Excel
        </Button>
      </div>

      <div>
        <CommonTable>
          <thead>
            <CommonTableMainHeaderRow>
              <th></th>
              <CommonTableHeadingMergeCell colSpan={3}>
                YTD vs. Budget
              </CommonTableHeadingMergeCell>
              <CommonTableHeadingMergeCell colSpan={3}>
                Full Year Expected vs. Budget
              </CommonTableHeadingMergeCell>
            </CommonTableMainHeaderRow>
            <CommonTableSubHeaderRow>
              <th className="text-start w-[150px]"></th>
              <CommonTableHeadingCell borderLeft className="w-[110px]">
                Actual
              </CommonTableHeadingCell>
              <CommonTableHeadingCell className="w-[110px]">
                Budget
              </CommonTableHeadingCell>
              <CommonTableHeadingCell className="w-[110px]">
                Variance
              </CommonTableHeadingCell>
              <CommonTableHeadingCell className="w-[110px]">
                Expected
              </CommonTableHeadingCell>
              <CommonTableHeadingCell className="w-[110px]">
                Budget
              </CommonTableHeadingCell>
              <CommonTableHeadingCell className="w-[110px]">
                Variance
              </CommonTableHeadingCell>
            </CommonTableSubHeaderRow>
          </thead>

          <tbody>
            {summaryData?.length === 0 ? (
              <ReportRowNoData colSpan={6} />
            ) : (
              regionSplitRowOrderTable?.map(({ label, key, unit }) => {
                const item = summaryData?.find(
                  (item) => item?.FeesType === key,
                );

                return (
                  <CommonTableBodyRow key={key}>
                    <CommonTableBodyCell textAlign="text-start" borderRight>
                      {/* {item?.FeesType} */}
                      {label}
                    </CommonTableBodyCell>
                    <CommonTableBodyCell>
                      {formatValue(item?.Actual, unit)}
                    </CommonTableBodyCell>
                    <CommonTableBodyCell>
                      {formatValue(item?.budget, unit)}
                    </CommonTableBodyCell>
                    <CommonTableBodyCell borderRight>
                      {formatValue(item?.Variance, unit)}
                    </CommonTableBodyCell>

                    <CommonTableBodyCell>
                      {formatValue(item?.forecast, unit)}
                    </CommonTableBodyCell>
                    <CommonTableBodyCell>
                      {formatValue(item?.budget1, unit)}
                    </CommonTableBodyCell>
                    <CommonTableBodyCell borderRight>
                      {formatValue(item?.Variance1, unit)}
                    </CommonTableBodyCell>
                  </CommonTableBodyRow>
                );
              })
            )}

            {/*  */}
          </tbody>
        </CommonTable>
      </div>
    </>
  );
}
