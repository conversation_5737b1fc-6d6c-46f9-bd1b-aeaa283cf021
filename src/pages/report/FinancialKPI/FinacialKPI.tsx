import { useEffect, useMemo, useState } from 'react';
import {
  financeKpiRegionActualApi,
  financeKpiRegionForecastApi,
  financeKpiRegionWiseApi,
  financeKpiYTDActualVsBudgetApi,
} from '@/api/financeKPIRegionApi/financeKPIRegionApi';
import {
  FinanceKPIPayload,
  FinanceKPIRegionActualResponse,
  FinanceKPIRegionForecastResponse,
  FinanceKPIRegionWiseResponce,
} from '@/api/financeKPIRegionApi/financeKPIRegionApi.types';
import { RootState } from '@/store';
import { useSelector } from 'react-redux';
import { toast } from 'sonner';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import { Container } from '@/components/common/container';
import ReportFilters from '../IncomeReport/components/ReportFilters';
import {
  ReportsTabsList,
  ReportTabsContentUILayout,
  ReportTabsTrigger,
} from '../ReportsCommonComponents/reportsTabsUi/ReportsTabsAtoms';
import FinancialYTDFullYearTable from './FinancialTables/FinancialYTDFullYearTable';
import MarketSplitTable from './FinancialTables/MarketSplitTable';
import RegionWiseSplitTable from './FinancialTables/RegionWiseSplitTable';

export default function FinacialKPI() {
  const { filters } = useSelector((state: RootState) => state.incomeReport);

  const [tableDataMarketRegionWise, setTableDataMarketRegionWise] = useState<
    FinanceKPIRegionWiseResponce[]
  >([]);

  const [tableDataYTDActualVsBudget, setTableDataYTDActualVsBudget] = useState<
    FinanceKPIRegionWiseResponce[]
  >([]);

  const [tableDataMarketRegionActual, setTableDataMarketRegionActual] =
    useState<FinanceKPIRegionActualResponse[]>([]);
  const [tableDataMarketRegionExpected, setTableDataMarketRegionExpected] =
    useState<FinanceKPIRegionForecastResponse[]>([]);

  const [loadingStates, setLoadingStates] = useState({
    regionWise: false,
    actual: false,
    forecast: false,
    ytdBudget: false,
  });

  const [errorStates, setErrorStates] = useState({
    regionWise: '',
    actual: '',
    forecast: '',
    ytdBudget: '',
  });

  const getMonthNumber = (monthName: string): string => {
    const months = {
      January: '1',
      February: '2',
      March: '3',
      April: '4',
      May: '5',
      June: '6',
      July: '7',
      August: '8',
      September: '9',
      October: '10',
      November: '11',
      December: '12',
    };

    return months[monthName as keyof typeof months] || '1';
  };

  const fetchRegionWiseData = async (body: FinanceKPIPayload) => {
    setLoadingStates((prev) => ({ ...prev, regionWise: true }));
    try {
      const res = await financeKpiRegionWiseApi(body);
      setTableDataMarketRegionWise(res.data);
    } catch (err) {
      setErrorStates((prev) => ({
        ...prev,
        regionWise: 'Failed to load Region Wise data',
      }));
      toast.error('Failed to load Region Wise data');
      console.log('Failed to load Region Wise data', err);
    } finally {
      setLoadingStates((prev) => ({ ...prev, regionWise: false }));
    }
  };

  const fetchActualData = async (body: FinanceKPIPayload) => {
    setLoadingStates((prev) => ({ ...prev, actual: true }));
    try {
      const res = await financeKpiRegionActualApi(body);
      setTableDataMarketRegionActual(res.data);
    } catch (err) {
      setErrorStates((prev) => ({
        ...prev,
        actual: 'Failed to load Actual data',
      }));
      toast.error('Failed to load Actual data');
      console.log('Failed to load Actual data', err);
    } finally {
      setLoadingStates((prev) => ({ ...prev, actual: false }));
    }
  };

  const fetchForecastData = async (body: FinanceKPIPayload) => {
    setLoadingStates((prev) => ({ ...prev, forecast: true }));
    try {
      const res = await financeKpiRegionForecastApi(body);
      setTableDataMarketRegionExpected(res.data);
    } catch (err) {
      setErrorStates((prev) => ({
        ...prev,
        forecast: 'Failed to load Forecast data',
      }));
      toast.error('Failed to load Forecast data');
      console.log('Failed to load Forecast data', err);
    } finally {
      setLoadingStates((prev) => ({ ...prev, forecast: false }));
    }
  };

  const fetchYTDData = async (body: FinanceKPIPayload) => {
    setLoadingStates((prev) => ({ ...prev, ytdBudget: true }));
    try {
      const res = await financeKpiYTDActualVsBudgetApi(body);
      setTableDataYTDActualVsBudget(res.data);
    } catch (err) {
      setErrorStates((prev) => ({
        ...prev,
        ytdBudget: 'Failed to load YTD data',
      }));
      toast.error('Failed to load YTD data');
      console.log('Failed to load YTD data', err);
    } finally {
      setLoadingStates((prev) => ({ ...prev, ytdBudget: false }));
    }
  };

  useEffect(() => {
    const fetchAllApiCalls = async () => {
      const monthNumbers =
        filters.month.length > 0
          ? filters.month.map((month) => getMonthNumber(month))
          : // : ['1'];
            [''];

      // const yearValue = filters.year || '2025';
      const yearValue = filters.year || '';

      const requestBody = {
        year: yearValue,
        month: monthNumbers.length === 1 ? monthNumbers[0] : monthNumbers,
        department:
          filters.department.length === 0
            ? null
            : filters.department.length === 1
              ? filters.department[0]
              : filters.department,
        businessType:
          filters.businessType.length === 0
            ? null
            : filters.businessType.length === 1
              ? filters.businessType[0]
              : filters.businessType,
        marketleader:
          filters.marketLeader.length === 0
            ? null
            : filters.marketLeader.length === 1
              ? filters.marketLeader[0]
              : filters.marketLeader,
        adminBu:
          filters.adminBU.length === 0
            ? null
            : filters.adminBU.length === 1
              ? filters.adminBU[0]
              : filters.adminBU,
      };

      fetchRegionWiseData(requestBody);
      fetchActualData(requestBody);
      fetchForecastData(requestBody);
      fetchYTDData(requestBody);
    };

    fetchAllApiCalls();
  }, [filters]);

  const datePeriod = useMemo(() => {
    const year = filters.year || '';
    const months = filters.month || [];

    if (months.length === 1) {
      return `${months[0]} ${year}`;
    } else if (months.length > 1) {
      return `${months[0]} - ${months[months.length - 1]} ${year}`;
    } else {
      return year;
    }
  }, [filters]);

  return (
    <Container title="Financial KPI's" width="fluid">
      <div className="bg-white shadow-md rounded-lg p-4">
        <div className="mb-6">
          <ReportFilters />
        </div>
        <div>
          <Tabs defaultValue="regionTab">
            <ReportsTabsList className="grid-cols-3">
              <ReportTabsTrigger
                tabName="Region Split- Year To Date and Full Year"
                value="regionTab"
              />
              <ReportTabsTrigger
                tabName="Market Split- Year To Date and Full Year"
                value="marketTab"
              />
              <ReportTabsTrigger
                tabName="YTD and Full Year Expected Actual VS Budget"
                value="YTDTab"
              />
            </ReportsTabsList>

            <TabsContent value="regionTab">
              <ReportTabsContentUILayout
                loading={loadingStates?.regionWise}
                error={errorStates?.regionWise}
                component={
                  <RegionWiseSplitTable
                    tableData={tableDataMarketRegionWise}
                    filters={filters}
                    datePeriod={datePeriod}
                  />
                }
              />
            </TabsContent>
            <TabsContent value="marketTab">
              <ReportTabsContentUILayout
                loading={loadingStates.actual || loadingStates.forecast}
                error={errorStates.actual || errorStates.forecast}
                component={
                  <MarketSplitTable
                    actualData={tableDataMarketRegionActual}
                    forecastData={tableDataMarketRegionExpected}
                    filters={filters}
                    datePeriod={datePeriod}
                  />
                }
              />
            </TabsContent>
            <TabsContent value="YTDTab">
              <ReportTabsContentUILayout
                loading={loadingStates.ytdBudget}
                error={errorStates.ytdBudget}
                component={
                  <FinancialYTDFullYearTable
                    summaryData={tableDataYTDActualVsBudget}
                    filters={filters}
                    datePeriod={datePeriod}
                  />
                }
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </Container>
  );
}
