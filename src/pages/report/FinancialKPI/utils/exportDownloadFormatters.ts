import {
  FinanceKPIRegionActualResponse,
  FinanceKPIRegionForecastResponse,
  FinanceKPIRegionWiseResponce,
} from '@/api/financeKPIRegionApi/financeKPIRegionApi.types';
import { ReportFilters } from '@/slice/incomeReportSlice';
import ExcelJS from 'exceljs';
import { toast } from 'sonner';
import { formatValue, regionSplitRowOrderTable } from './helperFinanceKpi';

const purpleColor = { argb: 'FF43298F' };
// const lightBlueColor = { argb: 'FFDEEFFF' };
const lightPurpleColor = { argb: 'FFF4F4FF' };
const whiteColor = { argb: 'FFFFFFFF' };

const companyRowFont = { bold: true, size: 16, color: purpleColor };

const titleRowFont = { bold: true, size: 14, color: purpleColor };
const periodRowFont = { bold: true, size: 12, color: purpleColor };
const consolidationFont = { bold: true, size: 12, color: purpleColor };

const headingFill = {
  type: 'pattern' as const,
  pattern: 'solid' as const,
  fgColor: purpleColor,
};

const rowLightPurpleColor = {
  type: 'pattern' as const,
  pattern: 'solid' as const,
  fgColor: lightPurpleColor,
};

const mainHeadingBorders = {
  top: { style: 'thin' } as const,
  left: { style: 'thin' } as const,
  bottom: { style: 'thin' } as const,
  right: { style: 'thin' } as const,
};
const headingMergeFont = { bold: true, color: whiteColor, size: 12 };

const cellSubHeadingFont = { bold: true, color: whiteColor, size: 11 };

const subHeaderRowHeight = 35;

const alignmentCenter = {
  horizontal: 'center' as const,
  vertical: 'middle' as const,
};
const alignmentRight = {
  horizontal: 'right' as const,
  vertical: 'middle' as const,
};
const alignmentLeft = {
  horizontal: 'left' as const,
  vertical: 'middle' as const,
};

const gropHeaderBroder = {
  style: 'thin' as ExcelJS.BorderStyle,
  color: { argb: 'FF000000' },
};

// Helper function to determine the consolidation header based on filter priority
const getConsolidationHeader = (filters?: {
  month: string[];
  year: string;
  department: string[];
  businessType: string[];
  marketLeader: string[];
  adminBU: string[];
}): string => {
  if (!filters) return 'Master Consolidation';

  // Priority order: Admin BU > Market Leader > Department > Business Type
  if (filters.adminBU && filters.adminBU.length > 0) {
    const values = filters.adminBU.join(', ');
    return `Filtered by Admin BU: ${values}`;
  }

  if (filters.marketLeader && filters.marketLeader.length > 0) {
    const values = filters.marketLeader.join(', ');
    return `Filtered by Market Leader: ${values}`;
  }

  if (filters.department && filters.department.length > 0) {
    const values = filters.department.join(', ');
    return `Filtered by Department: ${values}`;
  }

  if (filters.businessType && filters.businessType.length > 0) {
    const values = filters.businessType.join(', ');
    return `Filtered by Business Type: ${values}`;
  }

  return 'Master Consolidation';
};

const marketTableColumsWidth = [
  { width: 40 },

  { width: 15 },
  { width: 18 },
  { width: 15 },
  { width: 15 },
  { width: 15 },
  { width: 15 },

  { width: 15 },
  { width: 18 },
  { width: 15 },
  { width: 15 },
  { width: 15 },
  { width: 15 },

  { width: 15 },
  { width: 18 },
  { width: 15 },
  { width: 15 },
  { width: 15 },
  { width: 15 },
];

// The object mapping each FeesType to its KPIValue
type RegionKPIMap = {
  [feesType: string]: FinanceKPIRegionWiseResponce;
};

// The grouped structure — each item is a tuple: [RegionName, RegionKPIMap]
type GroupedData = [string, RegionKPIMap][];

export const exportRegionWiseSplitPropertyToExcel = async (
  groupedData: GroupedData,
  filters: ReportFilters,
) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Finance KPI Region-Split YTD');

  let displayMonth = '';
  const currentYear = filters.year || '2025';

  if (filters?.month?.length) {
    if (filters.month.length === 1) {
      displayMonth = filters.month[0];
    } else {
      displayMonth =
        filters.month[0] + ' - ' + filters.month[filters.month.length - 1];
    }
  } else {
    displayMonth = `January`;
  }

  const companyRow = worksheet.addRow(['Willow Bridge Property Company LLC']);
  companyRow.font = companyRowFont;
  worksheet.mergeCells('A1:G1');

  const titleRow = worksheet.addRow(['Region Split- YTD and Full Year']);
  titleRow.font = titleRowFont;
  worksheet.mergeCells('A2:G2');

  const periodRow = worksheet.addRow([
    `For the Period Ending ${displayMonth} ${currentYear}`,
  ]);
  periodRow.font = periodRowFont;
  worksheet.mergeCells('A3:G3');

  const consolidationRow = worksheet.addRow([getConsolidationHeader(filters)]);
  consolidationRow.font = consolidationFont;
  worksheet.mergeCells('A4:G4');

  worksheet.addRow([]);
  const groupHeaderRow = worksheet.addRow([
    '',
    'YTD Act. Vs. Budget',
    '',
    '',
    'Full Year Exp. Vs. Budget',
    '',
    '',
  ]);

  groupHeaderRow.eachCell((cell, colNum) => {
    if (colNum === 2 || colNum === 5) {
      cell.fill = headingFill;
      cell.font = headingMergeFont;
      cell.alignment = {
        horizontal: 'center',
        vertical: 'middle',
      };
      cell.border = mainHeadingBorders;
    }
  });
  const lastRow = worksheet?.lastRow?.number;
  worksheet.mergeCells(`B${lastRow}:D${lastRow}`);
  worksheet.mergeCells(`E${lastRow}:G${lastRow}`);

  // Set column widths
  worksheet.columns = [
    { width: 20 }, // Label
    { width: 15 }, // Actuals
    { width: 15 }, // Budget
    { width: 15 }, // Variance
    { width: 15 }, // Expected
    { width: 15 }, // Budget1
    { width: 15 }, // Variance1
  ];

  groupedData.forEach(([region, feesMap]) => {
    // Sub headers row
    const subHeader = worksheet.addRow([
      region?.toUpperCase(),
      'Actual',
      'Budget',
      'Variance',
      'Expected',
      'Budget',
      'Variance',
    ]);
    subHeader.height = subHeaderRowHeight;
    subHeader.eachCell((cell, colNum) => {
      if (colNum !== 1) {
        cell.font = cellSubHeadingFont;
        cell.fill = headingFill;
        cell.border = mainHeadingBorders;
        cell.alignment = alignmentCenter;
      } else {
        cell.font = { bold: true, underline: true, size:11 };
        cell.alignment = { vertical: 'middle' };
      }
    });

    // Data rows
    regionSplitRowOrderTable.forEach(({ label, key, unit }, index) => {
      const item = feesMap?.[key] || {};

      const rowCells = worksheet.addRow([
        label,
        formatValue(item?.Actual, unit, true),
        formatValue(item.budget, unit, true),
        formatValue(item?.Variance, unit, true),
        formatValue(item?.forecast, unit, true),
        formatValue(item?.budget1, unit, true),
        formatValue(item?.Variance1, unit, true),
      ]);

      if (index % 2 === 0) {
        rowCells.eachCell((cell) => {
          cell.fill = rowLightPurpleColor;
        });
      }

      rowCells.eachCell((cell, colNum) => {
        if (unit === '$') cell.numFmt = '"$"#,##0;("$"#,##0)';
        else if (unit === '%') cell.numFmt = '0%';
        else cell.numFmt = '#,##0;(#,##0)';

        if (colNum !== 1) {
          cell.alignment = { horizontal: 'right', vertical: 'middle' };
        } else {
          cell.alignment = { horizontal: 'left', vertical: 'middle' };
        }

        if (colNum === 1 || colNum === 4 || colNum === 7) {
          cell.border = {
            ...cell.border,
            right: { style: 'thin' },
          };
        }
      });

      if (index === regionSplitRowOrderTable.length - 1) {
        rowCells.eachCell((cell, colNum) => {
          if (colNum !== 1) {
            cell.border = {
              ...cell.border,
              bottom: { style: 'thin' },
            };
          }
        });
      }
    });
  });

  const displayMonthYear = `${filters.month?.[0] ?? 'January'} ${currentYear}`;
  // Write to Excel file
  const buffer = await workbook.xlsx.writeBuffer();
  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  });
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `Region_Split_YTD_${displayMonthYear.replace(' ', '_')}.xlsx`;
  a.click();
  window.URL.revokeObjectURL(url);
  toast.success('Excel export successful');
};

export const downloadExcelMarketRegionActualAndExpected = async ({
  actualData,
  forecastData,
  filters,
}: {
  actualData: FinanceKPIRegionActualResponse[];
  forecastData: FinanceKPIRegionForecastResponse[];
  filters: ReportFilters;
}) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Finance KPI Market Split YTD ');

  let displayMonth = '';
  const currentYear = filters.year || '2025';

  if (filters?.month?.length) {
    if (filters.month.length === 1) {
      displayMonth = filters.month[0];
    } else {
      displayMonth =
        filters.month[0] + ' - ' + filters.month[filters.month.length - 1];
    }
  } else {
    displayMonth = `January`;
  }

  const companyRow = worksheet.addRow(['Willow Bridge Property Company LLC']);
  companyRow.font = companyRowFont;
  worksheet.mergeCells('A1:S1');

  const titleRow = worksheet.addRow([
    'Market Split- Year To Date and Full Year',
  ]);
  titleRow.font = titleRowFont;
  worksheet.mergeCells('A2:S2');

  const periodRow = worksheet.addRow([
    `For the Period Ending ${displayMonth} ${currentYear}`,
  ]);
  periodRow.font = periodRowFont;
  worksheet.mergeCells('A3:S3');

  const consolidationRow = worksheet.addRow([getConsolidationHeader(filters)]);
  consolidationRow.font = consolidationFont;
  worksheet.mergeCells('A4:S4');
  worksheet.addRow(['']);
  worksheet.addRow(['']);

  const lastRow = worksheet?.lastRow?.number;

  worksheet.mergeCells(`B${lastRow}:G${lastRow}`);
  worksheet.mergeCells(`H${lastRow}:M${lastRow}`);
  worksheet.mergeCells(`N${lastRow}:S${lastRow}`);

  worksheet.getCell(`B${lastRow}`).value = 'YTD Actuals';
  worksheet.getCell(`B${lastRow}`).alignment = { horizontal: 'center' };

  worksheet.getCell(`H${lastRow}`).value = 'YTD Budget';
  worksheet.getCell(`H${lastRow}`).alignment = { horizontal: 'center' };
  worksheet.getCell(`N${lastRow}`).value = 'Variance';
  worksheet.getCell(`N${lastRow}`).alignment = { horizontal: 'center' };
  worksheet.getCell(`B${lastRow}`).fill = headingFill;
  worksheet.getCell(`H${lastRow}`).fill = headingFill;
  worksheet.getCell(`N${lastRow}`).fill = headingFill;
  worksheet.getCell(`B${lastRow}`).font = headingMergeFont;
  worksheet.getCell(`H${lastRow}`).font = headingMergeFont;
  worksheet.getCell(`N${lastRow}`).font = headingMergeFont;

  worksheet.getCell(`B${lastRow}`).border = mainHeadingBorders;
  worksheet.getCell(`H${lastRow}`).border = mainHeadingBorders;
  worksheet.getCell(`N${lastRow}`).border = mainHeadingBorders;

  const subHeadingsRow = [
    ' ',
    'PM Fees',
    'Return on Rev.',
    'Fee/Unit',
    'Cost/Door',
    'WAU',
    'Actual Units',
    // Budget
    'PM Fees',
    'Return on Rev.',
    'Fee/Unit',
    'Cost/Door',
    'WAU',
    'Actual Units',
    //  Variance
    'PM Fees',
    'Return on Rev.',
    'Fee/Unit',
    'Cost/Door',
    'WAU',
    'Actual Units',
  ];
  worksheet.columns = marketTableColumsWidth;
  const subheadings = worksheet.addRow(subHeadingsRow);
  subheadings.height = subHeaderRowHeight;

  subheadings.eachCell((cell, colNum) => {
    cell.alignment = { wrapText: true };
    if (colNum !== 1) {
      cell.font = cellSubHeadingFont;
      cell.fill = headingFill;
      cell.alignment = alignmentCenter;
      cell.border = mainHeadingBorders;
    }
  });

  actualData?.forEach((item, index) => {
    const actualDataBodyCells = worksheet.addRow([
      item?.regionmarket,

      formatValue(item?.['PM Fees_Actual'], '$', true),
      formatValue(item?.['ROR_Actual'], '%', true),
      formatValue(item?.['Fees/Unit_Actual'], '$', true),
      formatValue(item?.['Cost/Unit_Actual'], '$', true),
      formatValue(item?.['WAU_Actual'], '', true),
      formatValue(item?.['Actual Units_Actual'], '', true),

      formatValue(item?.['PM Fees_Budget'], '$', true),
      formatValue(item?.['ROR_Budget'], '%', true),
      formatValue(item?.['Fees/Unit_Budget'], '$', true),
      formatValue(item?.['Cost/Unit_Budget'], '$', true),
      formatValue(item?.['WAU_Budget'], '', true),
      formatValue(item?.['Actual Units_Budget'], '', true),

      formatValue(item?.['PM Fees_Varriance'], '$', true),
      formatValue(item?.['ROR_Varriance'], '%', true),
      formatValue(item?.['Fees/Unit_Varriance'], '$', true),
      formatValue(item?.['Cost/Unit_Varriance'], '$', true),
      formatValue(item?.['WAU_Varriance'], '', true),
      formatValue(item?.['Actual Units_Varriance'], '', true),
    ]);

    if (index % 2 === 0) {
      actualDataBodyCells.eachCell((cell) => {
        cell.fill = rowLightPurpleColor;
      });
    }

    actualDataBodyCells.eachCell((cell, colNum) => {
      const colName = subHeadingsRow[colNum - 1]; // colNum is 1-based

      if (colName === 'PM Fees') {
        cell.border = {
          left: gropHeaderBroder,
        };
      }
      if (colName === 'Actual Units') {
        cell.border = {
          right: gropHeaderBroder,
        };
      }

      if (
        colName === 'PM Fees' ||
        colName === 'Fee/Unit' ||
        colName === 'Cost/Door'
      ) {
        // cell.numFmt = '"$"#,##0';
        cell.numFmt = '"$"#,##0;("$"#,##0)';
      } else if (colName === 'Return on Rev.') {
        cell.numFmt = '0%';
      } else if (colName === 'WAU' || colName === 'Actual Units') {
        // cell.numFmt = '#,##0';
        cell.numFmt = '#,##0;(#,##0)';
      }

      if (colNum !== 1) {
        cell.alignment = alignmentRight;
      }
    });

    if (index === actualData.length - 1) {
      actualDataBodyCells.eachCell((cell, colNum) => {
        if (colNum !== 1) {
          cell.border = {
            ...cell.border,
            bottom: { style: 'thin' },
          };
        }
      });
    }
  });

  worksheet.addRow('');
  worksheet.addRow('');
  const groupHeaderRow = worksheet.addRow([]); // Adds an empty row

  const rowNumber = groupHeaderRow.number; // Get the row number just added

  worksheet.mergeCells(`B${rowNumber}:G${rowNumber}`);
  worksheet.mergeCells(`H${rowNumber}:M${rowNumber}`);
  worksheet.mergeCells(`N${rowNumber}:S${rowNumber}`);

  worksheet.getCell(`B${rowNumber}`).value = 'YTD Actuals';
  worksheet.getCell(`B${rowNumber}`).alignment = { horizontal: 'center' };
  worksheet.getCell(`H${rowNumber}`).value = 'YTD Budget';
  worksheet.getCell(`H${rowNumber}`).alignment = { horizontal: 'center' };
  worksheet.getCell(`N${rowNumber}`).value = 'Variance';
  worksheet.getCell(`N${rowNumber}`).alignment = { horizontal: 'center' };
  worksheet.getCell(`B${worksheet.lastRow?.number}`).fill = headingFill;
  worksheet.getCell(`H${worksheet.lastRow?.number}`).fill = headingFill;
  worksheet.getCell(`N${worksheet.lastRow?.number}`).fill = headingFill;
  worksheet.getCell(`B${worksheet.lastRow?.number}`).font = headingMergeFont;
  worksheet.getCell(`H${worksheet.lastRow?.number}`).font = headingMergeFont;
  worksheet.getCell(`N${worksheet.lastRow?.number}`).font = headingMergeFont;

  worksheet.getCell(`B${worksheet.lastRow?.number}`).border =
    mainHeadingBorders;
  worksheet.getCell(`H${worksheet.lastRow?.number}`).border =
    mainHeadingBorders;
  worksheet.getCell(`N${worksheet.lastRow?.number}`).border =
    mainHeadingBorders;

  const forecastSubheadings = worksheet.addRow(subHeadingsRow);
  forecastSubheadings.height = subHeaderRowHeight;

  forecastSubheadings.eachCell((cell, colNum) => {
    if (colNum !== 1) {
      cell.font = cellSubHeadingFont;
      cell.fill = headingFill;
      cell.alignment = alignmentCenter;
      cell.border = mainHeadingBorders;
    }
  });

  forecastData?.forEach((item, index) => {
    const forecastBodyCells = worksheet.addRow([
      item?.regionmarket,
      formatValue(item?.['PM Fees_Forecast'], '$', true),
      formatValue(item?.['ROR_Forecast'], '%', true),
      formatValue(item?.['Fees/Unit_Forecast'], '$', true),
      formatValue(item?.['Cost/Unit_Forecast'], '$', true),
      formatValue(item?.['WAU_Forecast'], '', true),
      formatValue(item?.['Actual Units_Forecast'], '', true),

      formatValue(item?.['PM Fees_Budget'], '$', true),
      formatValue(item?.['ROR_Budget'], '%', true),
      formatValue(item?.['Fees/Unit_Budget'], '$', true),
      formatValue(item?.['Cost/Unit_Budget'], '$', true),
      formatValue(item?.['WAU_Budget'], '', true),
      formatValue(item?.['Actual Units_Budget'], '', true),

      formatValue(item?.['PM Fees_Variance'], '$', true),
      formatValue(item?.['ROR_Variance'], '%', true),
      formatValue(item?.['Fees/Unit_Variance'], '$', true),
      formatValue(item?.['Cost/Unit_Variance'], '$', true),
      formatValue(item?.['WAU_Variance'], '', true),
      formatValue(item?.['Actual Units_Variance'], '', true),
    ]);

    if (index % 2 === 0) {
      forecastBodyCells.eachCell((cell) => {
        cell.fill = rowLightPurpleColor;
      });
    }

    forecastBodyCells.eachCell((cell, colnum) => {
      const colName = subHeadingsRow[colnum - 1]; // colNum is 1-based

      if (colName === 'PM Fees') {
        cell.border = {
          left: gropHeaderBroder,
        };
      }
      if (colName === 'Actual Units') {
        cell.border = {
          right: gropHeaderBroder,
        };
      }

      if (
        colName === 'PM Fees' ||
        colName === 'Fee/Unit' ||
        colName === 'Cost/Door'
      ) {
        cell.numFmt = '"$"#,##0;("$"#,##0)';
      } else if (colName === 'Return on Rev.') {
        cell.numFmt = '0%';
      } else if (colName === 'WAU' || colName === 'Actual Units') {
        cell.numFmt = '#,##0;(#,##0)';
      }

      if (colnum !== 1) {
        cell.alignment = alignmentRight;
      }
    });

    if (index === actualData.length - 1) {
      forecastBodyCells.eachCell((cell, colNum) => {
        if (colNum !== 1) {
          cell.border = {
            ...cell.border,
            bottom: { style: 'thin' },
          };
        }
      });
    }
  });

  const displayMonthYear = `${filters.month?.[0] ?? 'January'} ${currentYear}`;

  const buffer = await workbook.xlsx.writeBuffer();
  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  });

  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `Market_Split_${displayMonthYear.replace(' ', '_')}.xlsx`;
  a.click();
  window.URL.revokeObjectURL(url); // Clean up
};

export const downloadExcelYTDFullYear = async (
  summaryData: FinanceKPIRegionWiseResponce[],
  filters: ReportFilters,
) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet(
    'YTD and Full Year Expected Actual VS Budget',
  );

  let displayMonth = '';
  const currentYear = filters.year || '2025';

  if (filters?.month?.length) {
    if (filters.month.length === 1) {
      displayMonth = filters.month[0];
    } else {
      displayMonth =
        filters.month[0] + ' - ' + filters.month[filters.month.length - 1];
    }
  } else {
    displayMonth = `January`;
  }

  const companyRow = worksheet.addRow(['Willow Bridge Property Company LLC']);
  companyRow.font = companyRowFont;
  worksheet.mergeCells('A1:G1');

  const titleRow = worksheet.addRow([
    'YTD and Full Year Expected Actual VS Budget',
  ]);
  titleRow.font = titleRowFont;
  worksheet.mergeCells('A2:G2');

  const periodRow = worksheet.addRow([
    `For the Period Ending ${displayMonth} ${currentYear}`,
  ]);
  periodRow.font = periodRowFont;
  worksheet.mergeCells('A3:G3');

  const consolidationRow = worksheet.addRow([getConsolidationHeader(filters)]);
  consolidationRow.font = consolidationFont;
  worksheet.mergeCells('A4:G4');

  worksheet.addRow(['']);
  worksheet.addRow(['']);

  worksheet.mergeCells(
    `B${worksheet.lastRow?.number}:D${worksheet.lastRow?.number}`,
  );
  worksheet.mergeCells(
    `E${worksheet.lastRow?.number}:G${worksheet.lastRow?.number}`,
  );

  worksheet.getCell(`B${worksheet.lastRow?.number}`).value = 'YTD vs. Budget';
  worksheet.getCell(`E${worksheet.lastRow?.number}`).value =
    'Full Year Expected vs. Budget';

  worksheet.getCell(`B${worksheet.lastRow?.number}`).alignment =
    alignmentCenter;
  worksheet.getCell(`E${worksheet.lastRow?.number}`).alignment =
    alignmentCenter;

  worksheet.getCell(`B${worksheet.lastRow?.number}`).fill = headingFill;
  worksheet.getCell(`E${worksheet.lastRow?.number}`).fill = headingFill;
  worksheet.getCell(`B${worksheet.lastRow?.number}`).font = headingMergeFont;
  worksheet.getCell(`E${worksheet.lastRow?.number}`).font = headingMergeFont;
  worksheet.getCell(`B${worksheet.lastRow?.number}`).border =
    mainHeadingBorders;
  worksheet.getCell(`E${worksheet.lastRow?.number}`).border =
    mainHeadingBorders;

  const subHeadingsRow = [
    ' ',
    'Actual',
    'Budget',
    'Variance',

    // Budget
    'Expected',
    'Budget',
    'Variance',
  ];

  const subheadings = worksheet.addRow(subHeadingsRow);
  subheadings.height = subHeaderRowHeight;

  worksheet.columns = [
    { width: 18 },
    { width: 15 },
    { width: 15 },
    { width: 15 },
    { width: 15 },
    { width: 15 },
    { width: 15 },
  ];

  subheadings.eachCell((cell, colNum) => {
    if (colNum !== 1) {
      cell.font = cellSubHeadingFont;
      cell.fill = headingFill;
      cell.alignment = alignmentCenter;
      cell.border = mainHeadingBorders;
    }
  });

  regionSplitRowOrderTable.forEach(({ label, key, unit }, index) => {
    const item = summaryData?.find((item) => item?.FeesType === key);

    const rowCells = worksheet.addRow([
      label,
      formatValue(item?.Actual, unit, true),
      formatValue(item?.budget, unit, true),
      formatValue(item?.Variance, unit, true),
      formatValue(item?.forecast, unit, true),
      formatValue(item?.budget1, unit, true),
      formatValue(item?.Variance1, unit, true),
    ]);

    if (index % 2 === 0) {
      rowCells.eachCell((cell) => {
        cell.fill = rowLightPurpleColor;
      });
    }

    rowCells.eachCell((cell, colNum) => {
      if (unit === '$') cell.numFmt = '"$"#,##0;("$"#,##0)';
      else if (unit === '%') cell.numFmt = '0%';
      else cell.numFmt = '#,##0;(#,##0)';

      if (colNum !== 1) {
        cell.alignment = alignmentRight;
      } else {
        cell.alignment = alignmentLeft;
      }

      if (colNum === 1 || colNum === 4 || colNum === 7) {
        cell.border = {
          ...cell.border,
          right: { style: 'thin' },
        };
      }
    });
    if (index === regionSplitRowOrderTable.length - 1) {
      rowCells.eachCell((cell, colNum) => {
        if (colNum !== 1) {
          cell.border = {
            ...cell.border,
            bottom: { style: 'thin' },
          };
        }
      });
    }
  });

  const displayMonthYear = `${filters.month?.[0] ?? 'January'} ${currentYear}`;

  const buffer = await workbook.xlsx.writeBuffer();
  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  });

  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `YTD_Full_Year_${displayMonthYear.replace(' ', '_')}.xlsx`;
  a.click();
  window.URL.revokeObjectURL(url); // Clean up
};
