import { useEffect } from 'react';
import { AppRouting } from '@/routing/app-routing';
import { HelmetProvider } from 'react-helmet-async';
import { Provider, useDispatch, useSelector } from 'react-redux';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { LoadingBarContainer } from 'react-top-loading-bar';
import { PersistGate } from 'redux-persist/integration/react';
import { Toaster } from '@/components/ui/sonner';
import { checkTokenExpiration } from './helpers/AuthHelpers';
import { setupCrossTabLogoutListener } from './helpers/CrossTabHelper';
import { I18nProvider } from './providers/i18n-provider';
import { ModulesProvider } from './providers/modules-provider';
import { QueryProvider } from './providers/query-provider';
import { SettingsProvider } from './providers/settings-provider';
import { ThemeProvider } from './providers/theme-provider';
import { TooltipsProvider } from './providers/tooltips-provider';
import { persistor, RootState, store } from './store';

const { BASE_URL } = import.meta.env;

function TokenExpirationHandler() {
  const dispatch = useDispatch();
  const auth = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    let intervalId: NodeJS.Timeout | null = null;

    if (auth.tokenExpirationTime !== null) {
      const checkExpiration = () => {
        checkTokenExpiration(dispatch, auth.tokenExpirationTime);
      };

      intervalId = setInterval(checkExpiration, 1000);
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [dispatch, auth.tokenExpirationTime]);

  return null;
}

function AppContent() {
  useEffect(() => {
    const cleanup = setupCrossTabLogoutListener(store.dispatch);
    return cleanup;
  }, []);

  return (
    <SettingsProvider>
      <Provider store={store}>
        <PersistGate loading={null} persistor={persistor}>
          <TokenExpirationHandler />
          <ThemeProvider>
            <I18nProvider>
              <HelmetProvider>
                <TooltipsProvider>
                  <QueryProvider>
                    <LoadingBarContainer>
                      <BrowserRouter basename={BASE_URL}>
                        <Toaster />
                        <ModulesProvider>
                          <AppRouting />
                        </ModulesProvider>
                      </BrowserRouter>
                    </LoadingBarContainer>
                  </QueryProvider>
                </TooltipsProvider>
              </HelmetProvider>
            </I18nProvider>
          </ThemeProvider>
        </PersistGate>
      </Provider>
    </SettingsProvider>
  );
}

export function App() {
  return <AppContent />;
}
